"""
游戏设计文档

游戏名称：贪吃蛇

游戏规则：
1. 玩家通过键盘方向键控制蛇的移动方向。
2. 蛇需要吃掉屏幕上的食物，每吃一个食物，蛇的长度增加一格，得分增加一分。
3. 随着得分增加，蛇的移动速度会逐渐加快。
4. 游戏中有障碍物，蛇碰到障碍物或撞到墙壁会导致游戏结束。
5. 当玩家得分达到10分时，进入下一关卡，障碍物数量增加。
6. 游戏共有10个关卡，玩家通过所有关卡即为胜利。

界面布局：
1. 游戏界面分为游戏区域和显示栏。
2. 显示栏位于屏幕顶部，显示当前得分和关卡信息。
3. 游戏区域占据显示栏以下的部分，包含蛇、食物和障碍物。

主要功能点：
1. 蛇的移动：通过键盘方向键控制蛇的移动方向，蛇每次移动一个单位。
2. 食物生成：随机生成食物位置，食物被吃掉后重新生成。
3. 障碍物生成：根据关卡生成不同数量的障碍物，障碍物位置随机。
4. 碰撞检测：检测蛇是否碰到墙壁、障碍物或自身，碰撞则游戏结束。
5. 关卡管理：根据得分进入下一关卡，增加障碍物数量，重置蛇的位置和速度。
6. 界面绘制：绘制蛇、食物、障碍物和显示栏信息。
7. 游戏结束：显示游戏结束信息，玩家可以选择重新开始或退出游戏。
"""

import pygame
import time
import random

pygame.init()

# 屏幕尺寸
width = 800
height = 600
display_bar_height = 50  # 显示栏高度

# 颜色
white = (255, 255, 255)
yellow = (255, 255, 102)
black = (0, 0, 0)
red = (213, 50, 80)
green = (0, 255, 0)
blue = (50, 153, 213)
sky_blue = (135, 206, 235)  # 定义天蓝色

# 蛇块大小
snake_block = 10
initial_snake_speed = 8  # 初始速度较低
snake_speed = initial_snake_speed  # 初始化蛇的速度

# 字体
font_style = pygame.font.SysFont("bahnschrift", 25)
score_font = pygame.font.SysFont("comicsansms", 35)

# 关卡和障碍物
levels = 10
obstacles = []

def generate_obstacles(level):
    obstacles.clear()
    if level > 1:  # 第一关跳过障碍物生成
        for _ in range(level * 5):  # 随着关卡增加障碍物数量
            obs_x = round(random.randrange(0, width - snake_block) / 10.0) * 10.0
            obs_y = round(random.randrange(display_bar_height, height - snake_block) / 10.0) * 10.0
            obstacles.append([obs_x, obs_y])

def draw_obstacles():
    for obs in obstacles:
        pygame.draw.rect(dis, red, [obs[0], obs[1], snake_block, snake_block])

def our_snake(snake_block, snake_list):
    for x in snake_list:
        pygame.draw.rect(dis, black, [x[0], x[1], snake_block, snake_block])

def message(msg, color):
    mesg = font_style.render(msg, True, color)
    dis.blit(mesg, [width / 6, height / 3])

def Your_score(score):
    value = score_font.render("Your Score: " + str(score), True, black)
    dis.blit(value, [0, 0])  # 将分数移动到左上角

def display_level(level):
    value = score_font.render("Level: " + str(level), True, black)
    dis.blit(value, [width / 2 - 50, 0])  # 在中间显示关卡

def draw_origin():
    pygame.draw.circle(dis, blue, (0, display_bar_height), 5)  # 在(0, 0)处绘制一个蓝色圆点

def next_level_prompt():
    dis.fill(sky_blue)
    message("Level Complete! Press N-Next Level or Q-Quit", green)
    pygame.display.update()

def gameLoop():
    game_over = False
    game_close = False

    global snake_speed  # 将snake_speed设为全局变量以便在函数内修改
    snake_speed = initial_snake_speed  # 在游戏开始时重置蛇的速度

    x1 = width / 2
    y1 = height / 2 + display_bar_height  # 调整初始位置以适应显示栏
    x1_change = 0  # 初始化为静止
    y1_change = 0
    snake_List = []
    Length_of_snake = 1

    def reset_snake():
        nonlocal x1, y1, x1_change, y1_change, snake_List, Length_of_snake
        x1 = width / 2
        y1 = height / 2 + display_bar_height  # 调整初始位置以适应显示栏
        x1_change = 0  # 初始���为静止
        y1_change = 0
        snake_List = []
        Length_of_snake = 1

    reset_snake()  # 初始化蛇的位置和长度

    level = 1

    foodx = round(random.randrange(0, width - snake_block) / 10.0) * 10.0
    foody = round(random.randrange(display_bar_height, height - snake_block) / 10.0) * 10.0

    generate_obstacles(level)

    direction = None # 初始化方向

    while not game_over:

        while game_close == True:
            dis.fill(sky_blue)  # 将背景颜色改为天蓝色
            message("You Lost! Press Q-Quit or C-Play Again", red)
            pygame.display.update()

            for event in pygame.event.get():
                if event.type == pygame.KEYDOWN:
                    if event.key == pygame.K_q:
                        game_over = True
                        game_close = False
                    if event.key == pygame.K_c:
                        gameLoop()
                        return  # 确保当前游戏循环退出
                if event.type == pygame.QUIT:
                    game_over = True
                    game_close = False

        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                game_over = True
            if event.type == pygame.KEYDOWN:
                if event.key == pygame.K_LEFT and direction != 'RIGHT':
                    x1_change = -snake_block
                    y1_change = 0
                    direction = 'LEFT'
                elif event.key == pygame.K_RIGHT and direction != 'LEFT':
                    x1_change = snake_block
                    y1_change = 0
                    direction = 'RIGHT'
                elif event.key == pygame.K_UP and direction != 'DOWN':
                    y1_change = -snake_block
                    x1_change = 0
                    direction = 'UP'
                elif event.key == pygame.K_DOWN and direction != 'UP':
                    y1_change = snake_block
                    x1_change = 0
                    direction = 'DOWN'
        if x1 >= width or x1 < 0 or y1 >= height + display_bar_height or y1 < display_bar_height:  # 修改此行以防止蛇进入显示栏
            game_close = True
        x1 += x1_change
        y1 += y1_change
        dis.fill(sky_blue)  # 将背景颜色改为天蓝色
        pygame.draw.rect(dis, white, [0, 0, width, display_bar_height])  # 绘制显示栏
        draw_origin()  # 绘制原点
        pygame.draw.rect(dis, green, [foodx, foody, snake_block, snake_block])  # 绘制食物
        draw_obstacles()  # 绘制障碍物
        snake_Head = []
        snake_Head.append(x1)
        snake_Head.append(y1)
        snake_List.append(snake_Head)
        if len(snake_List) > Length_of_snake:
            del snake_List[0]

        for x in snake_List[:-1]:
            if x == snake_Head:
                game_close = True

        for obs in obstacles:
            if x1 == obs[0] and y1 == obs[1]:
                game_close = True

        our_snake(snake_block, snake_List)
        Your_score(Length_of_snake - 1)  # 显示分数
        display_level(level)  # 显示关卡

        pygame.display.update()

        if x1 == foodx and y1 == foody:
            foodx = round(random.randrange(0, width - snake_block) / 10.0) * 10.0
            foody = round(random.randrange(display_bar_height, height - snake_block) / 10.0) * 10.0
            Length_of_snake += 1
            snake_speed += 1  # 略微增加速度

            if Length_of_snake - 1 >= 10:  # 检查是否完成关卡
                level += 1
                if level > levels:
                    message("You Win! Press Q-Quit or C-Play Again", green)
                    pygame.display.update()
                    time.sleep(2)
                    game_close = True
                else:
                    next_level_prompt()
                    waiting_for_next_level = True
                    while waiting_for_next_level:
                        for event in pygame.event.get():
                            if event.type == pygame.KEYDOWN:
                                if event.key == pygame.K_n:
                                    waiting_for_next_level = False
                                if event.key == pygame.K_q:
                                    game_over = True
                                    game_close = False
                                    waiting_for_next_level = False
                            if event.type == pygame.QUIT:
                                game_over = True
                                game_close = False
                                waiting_for_next_level = False
                    if not game_over:
                        generate_obstacles(level)
                        reset_snake()  # 重置蛇的位置和长度
                        snake_speed = initial_snake_speed  # 将速度重置为初始速度

        clock.tick(snake_speed)

    pygame.quit()
    quit()

dis = pygame.display.set_mode((width, height + display_bar_height))  # 调整显示尺寸
pygame.display.set_caption('Snake Game')
clock = pygame.time.Clock()

gameLoop()
