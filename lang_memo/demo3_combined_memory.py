"""
Demo 3: <PERSON><PERSON><PERSON><PERSON> 长短期记忆结合演示
结合ConversationBufferMemory和向量数据库实现完整的记忆系统
"""

import os
import gradio as gr
from dotenv import load_dotenv
from langchain.chat_models import ChatOpenAI
from langchain.embeddings import OpenAIEmbeddings
from langchain.vectorstores import Chroma
from langchain.memory import ConversationBufferMemory
from langchain.chains import ConversationalRetrievalChain
from langchain.text_splitter import CharacterTextSplitter
from langchain.docstore.document import Document
import datetime

# 加载环境变量
load_dotenv()

class CombinedMemoryDemo:
    def __init__(self):
        # 初始化大模型
        self.llm = ChatOpenAI(
            openai_api_key=os.getenv("api_key"),
            openai_api_base=os.getenv("base_url"),
            model_name=os.getenv("model"),
            temperature=0.7
        )

        # 初始化嵌入模型
        self.embeddings = OpenAIEmbeddings(
            openai_api_key=os.getenv("api_key"),
            openai_api_base=os.getenv("base_url")
        )

        # 初始化向量数据库(长期记忆)
        self.vectorstore = Chroma(
            collection_name="combined_memory",
            embedding_function=self.embeddings,
            persist_directory="./chroma_db_combined"
        )

        # 初始化短期记忆
        self.memory = ConversationBufferMemory(
            memory_key="chat_history",
            return_messages=True,
            output_key="answer"
        )

        # 创建对话检索链(结合长短期记忆)
        self.qa_chain = ConversationalRetrievalChain.from_llm(
            llm=self.llm,
            retriever=self.vectorstore.as_retriever(search_kwargs={"k": 3}),
            memory=self.memory,
            return_source_documents=True,
            verbose=True
        )

        # 文本分割器
        self.text_splitter = CharacterTextSplitter(
            chunk_size=1000,
            chunk_overlap=200
        )
    
    def add_long_term_memory(self, content, category="general"):
        """添加长期记忆"""
        try:
            timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            doc = Document(
                page_content=content,
                metadata={
                    "category": category,
                    "timestamp": timestamp,
                    "source": "user_input"
                }
            )
            
            texts = self.text_splitter.split_documents([doc])
            self.vectorstore.add_documents(texts)
            self.vectorstore.persist()
            
            return f"成功添加长期记忆到类别 '{category}'"
        except Exception as e:
            return f"添加长期记忆失败: {str(e)}"
    
    def chat_with_memory(self, message, history):
        """使用长短期记忆进行对话"""
        try:
            # 使用结合了长短期记忆的链进行对话
            result = self.qa_chain({"question": message})
            
            answer = result["answer"]
            source_docs = result.get("source_documents", [])
            
            # 如果有相关的长期记忆，添加到回复中
            if source_docs:
                answer += "\n\n📚 相关长期记忆:"
                for i, doc in enumerate(source_docs[:2]):  # 只显示前2个相关记忆
                    metadata = doc.metadata
                    answer += f"\n{i+1}. [{metadata.get('category', 'unknown')}] {doc.page_content[:100]}..."
            
            history.append([message, answer])
            return "", history
        except Exception as e:
            error_msg = f"对话失败: {str(e)}"
            history.append([message, error_msg])
            return "", history
    
    def save_conversation_to_long_term(self, history):
        """将当前对话保存到长期记忆"""
        try:
            if not history:
                return "没有对话内容可保存"
            
            # 将对话历史转换为文本
            conversation_text = "对话记录:\n"
            for user_msg, ai_msg in history:
                conversation_text += f"用户: {user_msg}\nAI: {ai_msg}\n\n"
            
            # 保存到长期记忆
            result = self.add_long_term_memory(conversation_text, "conversation")
            return result
        except Exception as e:
            return f"保存对话失败: {str(e)}"
    
    def clear_short_term_memory(self):
        """清除短期记忆"""
        self.memory.clear()
        return [], "短期记忆已清除"
    
    def clear_long_term_memory(self):
        """清除长期记忆"""
        try:
            self.vectorstore.delete_collection()
            self.vectorstore = Chroma(
                collection_name="combined_memory",
                embedding_function=self.embeddings,
                persist_directory="./chroma_db_combined"
            )
            
            # 重新创建链
            self.qa_chain = ConversationalRetrievalChain.from_llm(
                llm=self.llm,
                retriever=self.vectorstore.as_retriever(search_kwargs={"k": 3}),
                memory=self.memory,
                return_source_documents=True,
                verbose=True
            )
            
            return "长期记忆已清除"
        except Exception as e:
            return f"清除长期记忆失败: {str(e)}"
    
    def show_memory_status(self):
        """显示记忆状态"""
        try:
            # 短期记忆状态
            short_term = self.memory.chat_memory.messages
            short_term_count = len(short_term)
            
            # 长期记忆状态
            long_term_docs = self.vectorstore.similarity_search("", k=100)
            long_term_count = len(long_term_docs)
            
            status = f"记忆状态报告:\n"
            status += f"📝 短期记忆: {short_term_count} 条消息\n"
            status += f"🗄️ 长期记忆: {long_term_count} 个文档片段\n\n"
            
            if short_term_count > 0:
                status += "最近的短期记忆:\n"
                for i, msg in enumerate(short_term[-4:]):  # 显示最近4条
                    role = "用户" if msg.type == "human" else "AI"
                    status += f"- {role}: {msg.content[:50]}...\n"
            
            return status
        except Exception as e:
            return f"获取记忆状态失败: {str(e)}"

def create_demo():
    """创建Gradio界面"""
    demo_instance = CombinedMemoryDemo()
    
    with gr.Blocks(title="长短期记忆结合Demo") as demo:
        gr.Markdown("# LangChain 长短期记忆结合演示")
        gr.Markdown("这个demo展示了如何结合使用短期记忆(对话历史)和长期记忆(向量数据库)")
        
        with gr.Tab("智能对话"):
            with gr.Row():
                with gr.Column(scale=2):
                    chatbot = gr.Chatbot(
                        label="智能对话 (结合长短期记忆)",
                        height=400
                    )
                    
                    with gr.Row():
                        msg = gr.Textbox(
                            label="输入消息",
                            placeholder="请输入您的问题...",
                            scale=4
                        )
                        send_btn = gr.Button("发送", scale=1)
                    
                    with gr.Row():
                        save_conv_btn = gr.Button("保存对话到长期记忆")
                        clear_short_btn = gr.Button("清除短期记忆")
                
                with gr.Column(scale=1):
                    memory_status = gr.Textbox(
                        label="记忆状态",
                        lines=15,
                        interactive=False
                    )
                    show_status_btn = gr.Button("刷新记忆状态")
        
        with gr.Tab("长期记忆管理"):
            with gr.Row():
                with gr.Column():
                    long_memory_content = gr.Textbox(
                        label="添加长期记忆",
                        lines=5,
                        placeholder="请输入要保存的长期记忆内容..."
                    )
                    long_memory_category = gr.Textbox(
                        label="记忆类别",
                        value="knowledge",
                        placeholder="例如: knowledge, personal, work等"
                    )
                    add_long_btn = gr.Button("添加长期记忆")
                    
                    with gr.Row():
                        clear_long_btn = gr.Button("清除长期记忆", variant="stop")
                    
                    long_memory_result = gr.Textbox(
                        label="操作结果",
                        lines=5,
                        interactive=False
                    )
        
        # 事件绑定
        send_btn.click(
            demo_instance.chat_with_memory,
            inputs=[msg, chatbot],
            outputs=[msg, chatbot]
        )
        
        msg.submit(
            demo_instance.chat_with_memory,
            inputs=[msg, chatbot],
            outputs=[msg, chatbot]
        )
        
        save_conv_btn.click(
            demo_instance.save_conversation_to_long_term,
            inputs=[chatbot],
            outputs=[long_memory_result]
        )
        
        clear_short_btn.click(
            demo_instance.clear_short_term_memory,
            outputs=[chatbot, long_memory_result]
        )
        
        add_long_btn.click(
            demo_instance.add_long_term_memory,
            inputs=[long_memory_content, long_memory_category],
            outputs=[long_memory_result]
        )
        
        clear_long_btn.click(
            demo_instance.clear_long_term_memory,
            outputs=[long_memory_result]
        )
        
        show_status_btn.click(
            demo_instance.show_memory_status,
            outputs=[memory_status]
        )
    
    return demo

if __name__ == "__main__":
    demo = create_demo()
    demo.launch(
        server_name="0.0.0.0",
        server_port=7863,
        share=False
    )
