#!/usr/bin/env python
# -*- coding: utf-8 -*-

print("开始测试导入...")

try:
    import gradio as gr
    print("✓ gradio 导入成功")
except ImportError as e:
    print(f"✗ gradio 导入失败: {e}")

try:
    from dotenv import load_dotenv
    print("✓ python-dotenv 导入成功")
except ImportError as e:
    print(f"✗ python-dotenv 导入失败: {e}")

try:
    from langchain.chat_models import ChatOpenAI
    print("✓ langchain.chat_models 导入成功")
except ImportError as e:
    print(f"✗ langchain.chat_models 导入失败: {e}")

try:
    from langchain.memory import ConversationBufferMemory
    print("✓ langchain.memory 导入成功")
except ImportError as e:
    print(f"✗ langchain.memory 导入失败: {e}")

try:
    from langchain.chains import ConversationChain
    print("✓ langchain.chains 导入成功")
except ImportError as e:
    print(f"✗ langchain.chains 导入失败: {e}")

print("测试完成！")
