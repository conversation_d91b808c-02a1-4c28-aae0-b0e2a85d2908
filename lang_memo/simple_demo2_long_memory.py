#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
简化版 Demo 2: 长期记忆演示
使用JSON文件实现持久化记忆存储，模拟向量数据库的功能
"""

import os
import gradio as gr
from dotenv import load_dotenv
import openai
import json
import datetime
from typing import List, Dict

# 加载环境变量
load_dotenv()

class SimpleLongTermMemory:
    """简单的长期记忆实现，使用JSON文件存储"""
    
    def __init__(self, memory_file="long_term_memory.json"):
        self.memory_file = memory_file
        self.memories = self.load_memories()
    
    def load_memories(self):
        """加载长期记忆"""
        try:
            if os.path.exists(self.memory_file):
                with open(self.memory_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return []
        except Exception as e:
            print(f"加载记忆失败: {e}")
            return []
    
    def save_memories(self):
        """保存长期记忆"""
        try:
            with open(self.memory_file, 'w', encoding='utf-8') as f:
                json.dump(self.memories, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存记忆失败: {e}")
    
    def add_memory(self, content, category="general"):
        """添加长期记忆"""
        timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        memory = {
            "id": len(self.memories) + 1,
            "content": content,
            "category": category,
            "timestamp": timestamp,
            "source": "user_input"
        }
        
        self.memories.append(memory)
        self.save_memories()
        return memory["id"]
    
    def search_memories(self, query, limit=5):
        """搜索相关记忆（简单的关键词匹配）"""
        query_lower = query.lower()
        relevant_memories = []
        
        for memory in self.memories:
            content_lower = memory["content"].lower()
            # 简单的关键词匹配
            if any(word in content_lower for word in query_lower.split()):
                relevant_memories.append(memory)
        
        return relevant_memories[:limit]
    
    def get_all_memories(self):
        """获取所有记忆"""
        return self.memories
    
    def clear_memories(self):
        """清除所有记忆"""
        self.memories = []
        self.save_memories()
    
    def get_memory_stats(self):
        """获取记忆统计信息"""
        if not self.memories:
            return "暂无记忆内容"
        
        categories = {}
        for memory in self.memories:
            category = memory.get("category", "unknown")
            categories[category] = categories.get(category, 0) + 1
        
        stats = f"总记忆数量: {len(self.memories)}\n\n"
        stats += "分类统计:\n"
        for category, count in categories.items():
            stats += f"- {category}: {count} 条\n"
        
        return stats

class LongTermMemoryDemo:
    def __init__(self):
        # 初始化OpenAI客户端
        self.client = openai.OpenAI(
            api_key=os.getenv("api_key"),
            base_url=os.getenv("base_url")
        )
        self.model = os.getenv("model")
        
        # 初始化长期记忆
        self.memory = SimpleLongTermMemory()
    
    def add_memory(self, content, category):
        """添加长期记忆"""
        try:
            if not content.strip():
                return "请输入记忆内容"
            
            memory_id = self.memory.add_memory(content, category)
            return f"✅ 成功添加记忆 (ID: {memory_id}) 到类别 '{category}'"
        except Exception as e:
            return f"❌ 添加记忆失败: {str(e)}"
    
    def query_memory(self, query):
        """查询长期记忆"""
        try:
            if not query.strip():
                return "请输入查询问题"
            
            # 搜索相关记忆
            relevant_memories = self.memory.search_memories(query)
            
            if not relevant_memories:
                return "没有找到相关记忆内容"
            
            # 构建上下文
            context = "相关记忆内容:\n"
            for i, memory in enumerate(relevant_memories):
                context += f"{i+1}. [{memory['category']}] {memory['content']}\n"
            
            # 使用AI基于记忆回答问题
            messages = [
                {"role": "system", "content": "你是一个智能助手，请基于提供的记忆内容回答用户的问题。"},
                {"role": "user", "content": f"问题: {query}\n\n{context}\n\n请基于上述记忆内容回答问题。"}
            ]
            
            response = self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                temperature=0.7,
                max_tokens=1000
            )
            
            ai_answer = response.choices[0].message.content
            
            # 格式化返回结果
            result = f"🤖 AI回答:\n{ai_answer}\n\n"
            result += "📚 相关记忆:\n"
            for i, memory in enumerate(relevant_memories):
                result += f"{i+1}. [{memory['category']}] {memory['timestamp']}\n"
                result += f"   {memory['content'][:100]}{'...' if len(memory['content']) > 100 else ''}\n\n"
            
            return result
        except Exception as e:
            return f"❌ 查询失败: {str(e)}"
    
    def list_memories(self):
        """列出所有记忆"""
        memories = self.memory.get_all_memories()
        if not memories:
            return "暂无记忆内容"
        
        result = "📋 所有记忆内容:\n\n"
        for memory in memories[-10:]:  # 只显示最近10条
            result += f"ID: {memory['id']} | 类别: {memory['category']} | 时间: {memory['timestamp']}\n"
            result += f"内容: {memory['content'][:100]}{'...' if len(memory['content']) > 100 else ''}\n\n"
        
        if len(memories) > 10:
            result += f"... 还有 {len(memories) - 10} 条记忆\n"
        
        return result
    
    def clear_memories(self):
        """清除所有记忆"""
        try:
            self.memory.clear_memories()
            return "✅ 所有长期记忆已清除"
        except Exception as e:
            return f"❌ 清除记忆失败: {str(e)}"
    
    def get_stats(self):
        """获取记忆统计"""
        return self.memory.get_memory_stats()

def create_demo():
    """创建Gradio界面"""
    demo_instance = LongTermMemoryDemo()
    
    with gr.Blocks(title="长期记忆Demo", theme=gr.themes.Soft()) as demo:
        gr.Markdown("# 🗄️ 长期记忆演示")
        gr.Markdown("这个demo展示了如何实现持久化记忆存储，可以保存知识并进行检索查询。")
        
        with gr.Tab("📝 添加记忆"):
            with gr.Row():
                with gr.Column():
                    memory_content = gr.Textbox(
                        label="记忆内容",
                        lines=5,
                        placeholder="请输入要保存的记忆内容..."
                    )
                    memory_category = gr.Textbox(
                        label="记忆类别",
                        value="general",
                        placeholder="例如: 个人信息, 工作, 学习, 生活等"
                    )
                    add_btn = gr.Button("💾 添加记忆", variant="primary")
                    add_result = gr.Textbox(
                        label="添加结果", 
                        interactive=False,
                        lines=3
                    )
        
        with gr.Tab("🔍 查询记忆"):
            with gr.Row():
                with gr.Column():
                    query_input = gr.Textbox(
                        label="查询问题",
                        placeholder="请输入您想查询的问题...",
                        lines=2
                    )
                    query_btn = gr.Button("🔍 查询记忆", variant="primary")
                    query_result = gr.Textbox(
                        label="查询结果",
                        lines=15,
                        interactive=False
                    )
        
        with gr.Tab("📊 记忆管理"):
            with gr.Row():
                with gr.Column():
                    with gr.Row():
                        list_btn = gr.Button("📋 查看所有记忆")
                        stats_btn = gr.Button("📊 记忆统计")
                        clear_btn = gr.Button("🗑️ 清除所有记忆", variant="stop")
                    
                    management_result = gr.Textbox(
                        label="操作结果",
                        lines=15,
                        interactive=False
                    )
        
        gr.Markdown("""
        ### 💡 使用说明：
        1. **添加记忆**: 在"添加记忆"标签页中输入要保存的内容和类别
        2. **查询记忆**: 在"查询记忆"标签页中输入问题，AI会基于相关记忆回答
        3. **记忆管理**: 在"记忆管理"标签页中查看、统计或清除记忆
        4. **持久化**: 所有记忆都会保存到本地文件，重启后依然存在
        """)
        
        # 事件绑定
        add_btn.click(
            demo_instance.add_memory,
            inputs=[memory_content, memory_category],
            outputs=[add_result]
        )
        
        query_btn.click(
            demo_instance.query_memory,
            inputs=[query_input],
            outputs=[query_result]
        )
        
        list_btn.click(
            demo_instance.list_memories,
            outputs=[management_result]
        )
        
        stats_btn.click(
            demo_instance.get_stats,
            outputs=[management_result]
        )
        
        clear_btn.click(
            demo_instance.clear_memories,
            outputs=[management_result]
        )
    
    return demo

if __name__ == "__main__":
    demo = create_demo()
    demo.launch(
        server_name="0.0.0.0",
        server_port=7862,
        share=False,
        show_error=True
    )
