"""
Demo 1: LangChain 短期记忆演示
使用ConversationBufferMemory实现对话历史记忆
"""

import os
import gradio as gr
from dotenv import load_dotenv
from langchain.chat_models import ChatOpenAI
from langchain.memory import ConversationBufferMemory
from langchain.chains import ConversationChain

# 加载环境变量
load_dotenv()

class ShortTermMemoryDemo:
    def __init__(self):
        # 初始化大模型
        self.llm = ChatOpenAI(
            openai_api_key=os.getenv("api_key"),
            openai_api_base=os.getenv("base_url"),
            model_name=os.getenv("model"),
            temperature=0.7
        )

        # 初始化短期记忆
        self.memory = ConversationBufferMemory(
            return_messages=True,
            memory_key="chat_history"
        )

        # 创建对话链
        self.conversation = ConversationChain(
            llm=self.llm,
            memory=self.memory,
            verbose=True
        )

    def chat(self, message, history):
        """处理用户输入并返回回复"""
        try:
            # 获取AI回复
            response = self.conversation.predict(input=message)

            # 更新历史记录
            history.append([message, response])

            return "", history
        except Exception as e:
            error_msg = f"错误: {str(e)}"
            history.append([message, error_msg])
            return "", history

    def clear_memory(self):
        """清除短期记忆"""
        self.memory.clear()
        return [], "记忆已清除"

    def show_memory(self):
        """显示当前记忆内容"""
        try:
            memory_content = self.memory.chat_memory.messages
            if not memory_content:
                return "当前没有记忆内容"

            memory_str = "当前记忆内容:\n"
            for i, msg in enumerate(memory_content):
                role = "用户" if msg.type == "human" else "AI"
                memory_str += f"{i+1}. {role}: {msg.content}\n"

            return memory_str
        except Exception as e:
            return f"获取记忆失败: {str(e)}"

def create_demo():
    """创建Gradio界面"""
    demo_instance = ShortTermMemoryDemo()
    
    with gr.Blocks(title="短期记忆Demo") as demo:
        gr.Markdown("# LangChain 短期记忆演示")
        gr.Markdown("这个demo展示了如何使用ConversationBufferMemory实现对话历史记忆")
        
        with gr.Row():
            with gr.Column(scale=2):
                chatbot = gr.Chatbot(
                    label="对话历史",
                    height=400
                )
                
                with gr.Row():
                    msg = gr.Textbox(
                        label="输入消息",
                        placeholder="请输入您的问题...",
                        scale=4
                    )
                    send_btn = gr.Button("发送", scale=1)
                
                with gr.Row():
                    clear_btn = gr.Button("清除记忆")
                    show_memory_btn = gr.Button("查看记忆")
            
            with gr.Column(scale=1):
                memory_display = gr.Textbox(
                    label="记忆状态",
                    lines=10,
                    interactive=False
                )
        
        # 事件绑定
        send_btn.click(
            demo_instance.chat,
            inputs=[msg, chatbot],
            outputs=[msg, chatbot]
        )
        
        msg.submit(
            demo_instance.chat,
            inputs=[msg, chatbot],
            outputs=[msg, chatbot]
        )
        
        clear_btn.click(
            demo_instance.clear_memory,
            outputs=[chatbot, memory_display]
        )
        
        show_memory_btn.click(
            demo_instance.show_memory,
            outputs=[memory_display]
        )
    
    return demo

if __name__ == "__main__":
    demo = create_demo()
    demo.launch(
        server_name="0.0.0.0",
        server_port=7861,
        share=False
    )
