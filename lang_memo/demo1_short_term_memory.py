"""
Demo 1: 短期记忆演示
使用简单的对话历史记忆实现
"""

import os
import gradio as gr
from dotenv import load_dotenv
import openai
import json

# 加载环境变量
load_dotenv()

class ShortTermMemoryDemo:
    def __init__(self):
        # 初始化OpenAI客户端
        self.client = openai.OpenAI(
            api_key=os.getenv("api_key"),
            base_url=os.getenv("base_url")
        )
        self.model = os.getenv("model")

        # 初始化短期记忆（对话历史）
        self.conversation_history = []

    def chat(self, message, history):
        """处理用户输入并返回回复"""
        try:
            # 添加用户消息到历史记录
            self.conversation_history.append({"role": "user", "content": message})

            # 构建消息列表（保留最近10轮对话）
            messages = [{"role": "system", "content": "你是一个有用的AI助手，能够记住对话历史。"}]
            messages.extend(self.conversation_history[-20:])  # 保留最近20条消息

            # 调用大模型
            response = self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                temperature=0.7,
                max_tokens=1000
            )

            ai_response = response.choices[0].message.content

            # 添加AI回复到历史记录
            self.conversation_history.append({"role": "assistant", "content": ai_response})

            # 更新界面历史记录
            history.append([message, ai_response])

            return "", history
        except Exception as e:
            error_msg = f"错误: {str(e)}"
            history.append([message, error_msg])
            return "", history

    def clear_memory(self):
        """清除短期记忆"""
        self.conversation_history = []
        return [], "记忆已清除"

    def show_memory(self):
        """显示当前记忆内容"""
        try:
            if not self.conversation_history:
                return "当前没有记忆内容"

            memory_str = "当前记忆内容:\n"
            for i, msg in enumerate(self.conversation_history):
                role = "用户" if msg["role"] == "user" else "AI"
                memory_str += f"{i+1}. {role}: {msg['content'][:100]}...\n"

            return memory_str
        except Exception as e:
            return f"获取记忆失败: {str(e)}"

def create_demo():
    """创建Gradio界面"""
    demo_instance = ShortTermMemoryDemo()
    
    with gr.Blocks(title="短期记忆Demo") as demo:
        gr.Markdown("# LangChain 短期记忆演示")
        gr.Markdown("这个demo展示了如何使用ConversationBufferMemory实现对话历史记忆")
        
        with gr.Row():
            with gr.Column(scale=2):
                chatbot = gr.Chatbot(
                    label="对话历史",
                    height=400
                )
                
                with gr.Row():
                    msg = gr.Textbox(
                        label="输入消息",
                        placeholder="请输入您的问题...",
                        scale=4
                    )
                    send_btn = gr.Button("发送", scale=1)
                
                with gr.Row():
                    clear_btn = gr.Button("清除记忆")
                    show_memory_btn = gr.Button("查看记忆")
            
            with gr.Column(scale=1):
                memory_display = gr.Textbox(
                    label="记忆状态",
                    lines=10,
                    interactive=False
                )
        
        # 事件绑定
        send_btn.click(
            demo_instance.chat,
            inputs=[msg, chatbot],
            outputs=[msg, chatbot]
        )
        
        msg.submit(
            demo_instance.chat,
            inputs=[msg, chatbot],
            outputs=[msg, chatbot]
        )
        
        clear_btn.click(
            demo_instance.clear_memory,
            outputs=[chatbot, memory_display]
        )
        
        show_memory_btn.click(
            demo_instance.show_memory,
            outputs=[memory_display]
        )
    
    return demo

if __name__ == "__main__":
    demo = create_demo()
    demo.launch(
        server_name="0.0.0.0",
        server_port=7861,
        share=False
    )
