#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
简化版 Demo 3: 长短期记忆结合演示
结合对话历史记忆和持久化记忆存储，实现完整的记忆系统
"""

import os
import gradio as gr
from dotenv import load_dotenv
import openai
import json
import datetime
from typing import List, Dict

# 加载环境变量
load_dotenv()

class CombinedMemorySystem:
    """结合短期和长期记忆的系统"""
    
    def __init__(self, memory_file="combined_memory.json"):
        # 短期记忆（对话历史）
        self.conversation_history = []
        
        # 长期记忆（持久化存储）
        self.memory_file = memory_file
        self.long_term_memories = self.load_long_term_memories()
    
    def load_long_term_memories(self):
        """加载长期记忆"""
        try:
            if os.path.exists(self.memory_file):
                with open(self.memory_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return []
        except Exception as e:
            print(f"加载长期记忆失败: {e}")
            return []
    
    def save_long_term_memories(self):
        """保存长期记忆"""
        try:
            with open(self.memory_file, 'w', encoding='utf-8') as f:
                json.dump(self.long_term_memories, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存长期记忆失败: {e}")
    
    def add_to_short_term(self, role, content):
        """添加到短期记忆"""
        self.conversation_history.append({
            "role": role,
            "content": content,
            "timestamp": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        })
    
    def add_to_long_term(self, content, category="conversation"):
        """添加到长期记忆"""
        memory = {
            "id": len(self.long_term_memories) + 1,
            "content": content,
            "category": category,
            "timestamp": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "source": "user_input"
        }
        self.long_term_memories.append(memory)
        self.save_long_term_memories()
        return memory["id"]
    
    def search_long_term_memories(self, query, limit=3):
        """搜索长期记忆"""
        query_lower = query.lower()
        relevant_memories = []
        
        for memory in self.long_term_memories:
            content_lower = memory["content"].lower()
            if any(word in content_lower for word in query_lower.split()):
                relevant_memories.append(memory)
        
        return relevant_memories[:limit]
    
    def get_short_term_context(self, limit=10):
        """获取短期记忆上下文"""
        return self.conversation_history[-limit:] if len(self.conversation_history) > limit else self.conversation_history
    
    def clear_short_term(self):
        """清除短期记忆"""
        self.conversation_history = []
    
    def clear_long_term(self):
        """清除长期记忆"""
        self.long_term_memories = []
        self.save_long_term_memories()
    
    def save_conversation_to_long_term(self):
        """将当前对话保存到长期记忆"""
        if not self.conversation_history:
            return None
        
        conversation_text = "对话记录:\n"
        for msg in self.conversation_history:
            role = "用户" if msg["role"] == "user" else "AI"
            conversation_text += f"{role}: {msg['content']}\n"
        
        return self.add_to_long_term(conversation_text, "conversation")
    
    def get_memory_status(self):
        """获取记忆状态"""
        short_count = len(self.conversation_history)
        long_count = len(self.long_term_memories)
        
        status = f"📊 记忆状态统计:\n"
        status += f"💭 短期记忆: {short_count} 条消息\n"
        status += f"🗄️ 长期记忆: {long_count} 个记忆片段\n\n"
        
        if short_count > 0:
            status += "📝 最近的短期记忆:\n"
            for msg in self.conversation_history[-3:]:
                role = "用户" if msg["role"] == "user" else "AI"
                content = msg["content"][:50] + "..." if len(msg["content"]) > 50 else msg["content"]
                status += f"- {role}: {content}\n"
        
        return status

class CombinedMemoryDemo:
    def __init__(self):
        # 初始化OpenAI客户端
        self.client = openai.OpenAI(
            api_key=os.getenv("api_key"),
            base_url=os.getenv("base_url")
        )
        self.model = os.getenv("model")
        
        # 初始化记忆系统
        self.memory_system = CombinedMemorySystem()
    
    def chat_with_memory(self, message, history):
        """使用长短期记忆进行对话"""
        try:
            # 添加用户消息到短期记忆
            self.memory_system.add_to_short_term("user", message)
            
            # 搜索相关的长期记忆
            relevant_memories = self.memory_system.search_long_term_memories(message)
            
            # 构建消息上下文
            system_prompt = "你是一个智能助手，能够记住对话历史并利用长期记忆回答问题。"
            
            # 添加长期记忆上下文
            if relevant_memories:
                system_prompt += "\n\n相关长期记忆:\n"
                for memory in relevant_memories:
                    system_prompt += f"- [{memory['category']}] {memory['content']}\n"
            
            # 构建完整的消息列表
            messages = [{"role": "system", "content": system_prompt}]
            
            # 添加短期记忆（对话历史）
            short_term_context = self.memory_system.get_short_term_context()
            for msg in short_term_context:
                messages.append({"role": msg["role"], "content": msg["content"]})
            
            # 调用大模型
            response = self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                temperature=0.7,
                max_tokens=1000
            )
            
            ai_response = response.choices[0].message.content
            
            # 如果使用了长期记忆，在回复中标注
            if relevant_memories:
                ai_response += f"\n\n💡 *基于 {len(relevant_memories)} 条长期记忆回答*"
            
            # 添加AI回复到短期记忆
            self.memory_system.add_to_short_term("assistant", ai_response)
            
            # 更新界面历史记录
            history.append([message, ai_response])
            
            return "", history
        except Exception as e:
            error_msg = f"❌ 对话失败: {str(e)}"
            history.append([message, error_msg])
            return "", history
    
    def add_long_term_memory(self, content, category):
        """添加长期记忆"""
        try:
            if not content.strip():
                return "请输入记忆内容"
            
            memory_id = self.memory_system.add_to_long_term(content, category)
            return f"✅ 成功添加长期记忆 (ID: {memory_id}) 到类别 '{category}'"
        except Exception as e:
            return f"❌ 添加长期记忆失败: {str(e)}"
    
    def save_conversation_to_long_term(self, history):
        """将当前对话保存到长期记忆"""
        try:
            if not self.memory_system.conversation_history:
                return "❌ 没有对话内容可保存"
            
            memory_id = self.memory_system.save_conversation_to_long_term()
            return f"✅ 对话已保存到长期记忆 (ID: {memory_id})"
        except Exception as e:
            return f"❌ 保存对话失败: {str(e)}"
    
    def clear_short_term_memory(self):
        """清除短期记忆"""
        self.memory_system.clear_short_term()
        return [], "✅ 短期记忆已清除"
    
    def clear_long_term_memory(self):
        """清除长期记忆"""
        try:
            self.memory_system.clear_long_term()
            return "✅ 长期记忆已清除"
        except Exception as e:
            return f"❌ 清除长期记忆失败: {str(e)}"
    
    def show_memory_status(self):
        """显示记忆状态"""
        return self.memory_system.get_memory_status()

def create_demo():
    """创建Gradio界面"""
    demo_instance = CombinedMemoryDemo()
    
    with gr.Blocks(title="长短期记忆结合Demo", theme=gr.themes.Soft()) as demo:
        gr.Markdown("# 🧠🗄️ 长短期记忆结合演示")
        gr.Markdown("这个demo展示了如何结合使用短期记忆(对话历史)和长期记忆(持久化存储)，实现完整的智能记忆系统。")
        
        with gr.Tab("💬 智能对话"):
            with gr.Row():
                with gr.Column(scale=2):
                    chatbot = gr.Chatbot(
                        label="🤖 智能对话 (结合长短期记忆)",
                        height=400,
                        show_label=True
                    )
                    
                    with gr.Row():
                        msg = gr.Textbox(
                            label="输入消息",
                            placeholder="请输入您的问题...",
                            scale=4,
                            lines=2
                        )
                        send_btn = gr.Button("发送", scale=1, variant="primary")
                    
                    with gr.Row():
                        save_conv_btn = gr.Button("💾 保存对话到长期记忆")
                        clear_short_btn = gr.Button("🗑️ 清除短期记忆")
                
                with gr.Column(scale=1):
                    memory_status = gr.Textbox(
                        label="📊 记忆状态",
                        lines=15,
                        interactive=False,
                        placeholder="点击'刷新记忆状态'查看当前记忆情况"
                    )
                    show_status_btn = gr.Button("🔄 刷新记忆状态")
        
        with gr.Tab("🗄️ 长期记忆管理"):
            with gr.Row():
                with gr.Column():
                    long_memory_content = gr.Textbox(
                        label="添加长期记忆",
                        lines=5,
                        placeholder="请输入要保存的长期记忆内容..."
                    )
                    long_memory_category = gr.Textbox(
                        label="记忆类别",
                        value="knowledge",
                        placeholder="例如: knowledge, personal, work, study等"
                    )
                    add_long_btn = gr.Button("💾 添加长期记忆", variant="primary")
                    
                    with gr.Row():
                        clear_long_btn = gr.Button("🗑️ 清除长期记忆", variant="stop")
                    
                    long_memory_result = gr.Textbox(
                        label="操作结果",
                        lines=8,
                        interactive=False
                    )
        
        gr.Markdown("""
        ### 💡 使用说明：
        1. **智能对话**: AI会同时利用对话历史和相关长期记忆进行回复
        2. **保存对话**: 将有价值的对话保存到长期记忆中
        3. **记忆管理**: 可以手动添加长期记忆，也可以清除不同类型的记忆
        4. **记忆状态**: 实时查看短期和长期记忆的使用情况
        5. **智能检索**: AI会自动搜索相关的长期记忆来辅助回答
        """)
        
        # 事件绑定
        send_btn.click(
            demo_instance.chat_with_memory,
            inputs=[msg, chatbot],
            outputs=[msg, chatbot]
        )
        
        msg.submit(
            demo_instance.chat_with_memory,
            inputs=[msg, chatbot],
            outputs=[msg, chatbot]
        )
        
        save_conv_btn.click(
            demo_instance.save_conversation_to_long_term,
            inputs=[chatbot],
            outputs=[long_memory_result]
        )
        
        clear_short_btn.click(
            demo_instance.clear_short_term_memory,
            outputs=[chatbot, long_memory_result]
        )
        
        add_long_btn.click(
            demo_instance.add_long_term_memory,
            inputs=[long_memory_content, long_memory_category],
            outputs=[long_memory_result]
        )
        
        clear_long_btn.click(
            demo_instance.clear_long_term_memory,
            outputs=[long_memory_result]
        )
        
        show_status_btn.click(
            demo_instance.show_memory_status,
            outputs=[memory_status]
        )
    
    return demo

if __name__ == "__main__":
    demo = create_demo()
    demo.launch(
        server_name="0.0.0.0",
        server_port=7863,
        share=False,
        show_error=True
    )
