#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
启动脚本 - 运行简化版记忆演示
"""

import subprocess
import sys
import time
import threading

def run_demo(script_name, port):
    """运行单个demo"""
    try:
        print(f"启动 {script_name} 在端口 {port}...")
        subprocess.run([sys.executable, script_name], check=True)
    except KeyboardInterrupt:
        print(f"\n{script_name} 被用户中断")
    except Exception as e:
        print(f"运行 {script_name} 时出错: {e}")

def main():
    print("=" * 60)
    print("🧠 记忆系统演示启动器 (简化版)")
    print("=" * 60)
    print("1. Demo 1: 短期记忆演示 (端口 7861)")
    print("   - 模拟LangChain的ConversationBufferMemory")
    print("   - 实现对话历史记忆功能")
    print()
    print("2. Demo 2: 长期记忆演示 (端口 7862)")
    print("   - 模拟向量数据库的持久化存储")
    print("   - 实现知识检索功能")
    print()
    print("3. Demo 3: 长短期记忆结合演示 (端口 7863)")
    print("   - 结合对话历史和知识库")
    print("   - 实现完整的智能记忆系统")
    print()
    print("4. 同时启动所有演示")
    print("=" * 60)
    
    choice = input("请选择要运行的演示 (1-4): ").strip()
    
    if choice == "1":
        print("🚀 启动短期记忆演示...")
        run_demo("simple_demo1_short_memory.py", 7861)
    elif choice == "2":
        print("🚀 启动长期记忆演示...")
        run_demo("simple_demo2_long_memory.py", 7862)
    elif choice == "3":
        print("🚀 启动长短期记忆结合演示...")
        run_demo("simple_demo3_combined_memory.py", 7863)
    elif choice == "4":
        print("🚀 同时启动所有演示...")
        print()
        print("请在浏览器中访问:")
        print("- 短期记忆演示: http://localhost:7861")
        print("- 长期记忆演示: http://localhost:7862")
        print("- 长短期记忆结合演示: http://localhost:7863")
        print()
        print("按 Ctrl+C 停止所有演示")
        
        # 创建线程来运行每个demo
        demos = [
            ("simple_demo1_short_memory.py", 7861),
            ("simple_demo2_long_memory.py", 7862),
            ("simple_demo3_combined_memory.py", 7863)
        ]
        
        threads = []
        for script, port in demos:
            thread = threading.Thread(target=run_demo, args=(script, port))
            thread.daemon = True
            thread.start()
            threads.append(thread)
            time.sleep(2)  # 延迟启动避免端口冲突
        
        try:
            # 等待所有线程
            for thread in threads:
                thread.join()
        except KeyboardInterrupt:
            print("\n正在停止所有演示...")
    else:
        print("❌ 无效选择，请重新运行脚本")

if __name__ == "__main__":
    main()
