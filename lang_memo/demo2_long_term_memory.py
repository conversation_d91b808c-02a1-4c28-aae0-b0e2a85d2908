"""
Demo 2: 长期记忆演示
使用JSON文件实现持久化记忆存储
"""

import os
import gradio as gr
from dotenv import load_dotenv
import openai
import json
import datetime
from typing import List, Dict

# 加载环境变量
load_dotenv()

class LongTermMemoryDemo:
    def __init__(self):
        # 初始化OpenAI客户端
        self.client = openai.OpenAI(
            api_key=os.getenv("api_key"),
            base_url=os.getenv("base_url")
        )
        self.model = os.getenv("model")

        # 长期记忆存储文件
        self.memory_file = "long_term_memory.json"
        self.memories = self.load_memories()

    def load_memories(self):
        """加载长期记忆"""
        try:
            if os.path.exists(self.memory_file):
                with open(self.memory_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return []
        except Exception as e:
            print(f"加载记忆失败: {e}")
            return []

    def save_memories(self):
        """保存长期记忆"""
        try:
            with open(self.memory_file, 'w', encoding='utf-8') as f:
                json.dump(self.memories, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存记忆失败: {e}")

    def add_memory(self, content, category="general"):
        """添加长期记忆"""
        try:
            timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            memory = {
                "id": len(self.memories) + 1,
                "content": content,
                "category": category,
                "timestamp": timestamp,
                "source": "user_input"
            }

            self.memories.append(memory)
            self.save_memories()

            return f"成功添加记忆到类别 '{category}'"
        except Exception as e:
            return f"添加记忆失败: {str(e)}"
    
    def query_memory(self, query):
        """查询长期记忆"""
        try:
            # 使用QA链查询
            result = self.qa_chain({"query": query})
            
            answer = result["result"]
            sources = result["source_documents"]
            
            # 格式化源文档信息
            source_info = "\n\n相关记忆片段:\n"
            for i, doc in enumerate(sources):
                metadata = doc.metadata
                source_info += f"{i+1}. 类别: {metadata.get('category', 'unknown')}\n"
                source_info += f"   时间: {metadata.get('timestamp', 'unknown')}\n"
                source_info += f"   内容: {doc.page_content[:200]}...\n\n"
            
            return answer + source_info
        except Exception as e:
            return f"查询记忆失败: {str(e)}"
    
    def list_memories(self):
        """列出所有记忆"""
        try:
            # 获取所有文档
            results = self.vectorstore.similarity_search("", k=100)
            
            if not results:
                return "暂无记忆内容"
            
            memory_list = "所有记忆内容:\n\n"
            for i, doc in enumerate(results):
                metadata = doc.metadata
                memory_list += f"{i+1}. 类别: {metadata.get('category', 'unknown')}\n"
                memory_list += f"   时间: {metadata.get('timestamp', 'unknown')}\n"
                memory_list += f"   内容: {doc.page_content[:100]}...\n\n"
            
            return memory_list
        except Exception as e:
            return f"获取记忆列表失败: {str(e)}"
    
    def clear_memories(self):
        """清除所有记忆"""
        try:
            # 删除集合
            self.vectorstore.delete_collection()
            
            # 重新初始化
            self.vectorstore = Chroma(
                collection_name="long_term_memory",
                embedding_function=self.embeddings,
                persist_directory="./chroma_db"
            )
            
            # 重新创建QA链
            self.qa_chain = RetrievalQA.from_chain_type(
                llm=self.llm,
                chain_type="stuff",
                retriever=self.vectorstore.as_retriever(search_kwargs={"k": 3}),
                return_source_documents=True
            )
            
            return "所有记忆已清除"
        except Exception as e:
            return f"清除记忆失败: {str(e)}"

def create_demo():
    """创建Gradio界面"""
    demo_instance = LongTermMemoryDemo()
    
    with gr.Blocks(title="长期记忆Demo") as demo:
        gr.Markdown("# LangChain 长期记忆演示")
        gr.Markdown("这个demo展示了如何使用向量数据库实现持久化记忆存储")
        
        with gr.Tab("添加记忆"):
            with gr.Row():
                with gr.Column():
                    memory_content = gr.Textbox(
                        label="记忆内容",
                        lines=5,
                        placeholder="请输入要保存的记忆内容..."
                    )
                    memory_category = gr.Textbox(
                        label="记忆类别",
                        value="general",
                        placeholder="例如: 个人信息, 工作, 学习等"
                    )
                    add_btn = gr.Button("添加记忆")
                    add_result = gr.Textbox(label="添加结果", interactive=False)
        
        with gr.Tab("查询记忆"):
            with gr.Row():
                with gr.Column():
                    query_input = gr.Textbox(
                        label="查询问题",
                        placeholder="请输入您想查询的问题..."
                    )
                    query_btn = gr.Button("查询记忆")
                    query_result = gr.Textbox(
                        label="查询结果",
                        lines=10,
                        interactive=False
                    )
        
        with gr.Tab("记忆管理"):
            with gr.Row():
                with gr.Column():
                    list_btn = gr.Button("查看所有记忆")
                    clear_btn = gr.Button("清除所有记忆", variant="stop")
                    management_result = gr.Textbox(
                        label="操作结果",
                        lines=15,
                        interactive=False
                    )
        
        # 事件绑定
        add_btn.click(
            demo_instance.add_memory,
            inputs=[memory_content, memory_category],
            outputs=[add_result]
        )
        
        query_btn.click(
            demo_instance.query_memory,
            inputs=[query_input],
            outputs=[query_result]
        )
        
        list_btn.click(
            demo_instance.list_memories,
            outputs=[management_result]
        )
        
        clear_btn.click(
            demo_instance.clear_memories,
            outputs=[management_result]
        )
    
    return demo

if __name__ == "__main__":
    demo = create_demo()
    demo.launch(
        server_name="0.0.0.0",
        server_port=7862,
        share=False
    )
