#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
简化版 Demo 1: 短期记忆演示
使用简单的对话历史记忆实现，模拟LangChain的ConversationBufferMemory
"""

import os
import gradio as gr
from dotenv import load_dotenv
import openai
import json

# 加载环境变量
load_dotenv()

class SimpleShortTermMemory:
    """简单的短期记忆实现，模拟LangChain的ConversationBufferMemory"""
    
    def __init__(self):
        self.messages = []  # 存储对话历史
    
    def add_user_message(self, content):
        """添加用户消息"""
        self.messages.append({"role": "user", "content": content})
    
    def add_ai_message(self, content):
        """添加AI消息"""
        self.messages.append({"role": "assistant", "content": content})
    
    def get_messages(self, limit=20):
        """获取最近的消息（限制数量避免token过多）"""
        return self.messages[-limit:] if len(self.messages) > limit else self.messages
    
    def clear(self):
        """清除记忆"""
        self.messages = []
    
    def get_memory_summary(self):
        """获取记忆摘要"""
        if not self.messages:
            return "当前没有记忆内容"
        
        summary = f"记忆中共有 {len(self.messages)} 条消息:\n\n"
        for i, msg in enumerate(self.messages):
            role = "用户" if msg["role"] == "user" else "AI"
            content = msg["content"][:50] + "..." if len(msg["content"]) > 50 else msg["content"]
            summary += f"{i+1}. {role}: {content}\n"
        
        return summary

class ShortTermMemoryDemo:
    def __init__(self):
        # 初始化OpenAI客户端
        self.client = openai.OpenAI(
            api_key=os.getenv("api_key"),
            base_url=os.getenv("base_url")
        )
        self.model = os.getenv("model")
        
        # 初始化短期记忆
        self.memory = SimpleShortTermMemory()
    
    def chat(self, message, history):
        """处理用户输入并返回回复"""
        try:
            # 添加用户消息到记忆
            self.memory.add_user_message(message)
            
            # 构建消息列表
            system_message = {"role": "system", "content": "你是一个有用的AI助手，能够记住对话历史并基于历史进行回复。"}
            messages = [system_message] + self.memory.get_messages()
            
            # 调用大模型
            response = self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                temperature=0.7,
                max_tokens=1000
            )
            
            ai_response = response.choices[0].message.content
            
            # 添加AI回复到记忆
            self.memory.add_ai_message(ai_response)
            
            # 更新界面历史记录
            history.append([message, ai_response])
            
            return "", history
        except Exception as e:
            error_msg = f"错误: {str(e)}"
            history.append([message, error_msg])
            return "", history
    
    def clear_memory(self):
        """清除短期记忆"""
        self.memory.clear()
        return [], "短期记忆已清除！现在开始新的对话。"
    
    def show_memory(self):
        """显示当前记忆内容"""
        return self.memory.get_memory_summary()

def create_demo():
    """创建Gradio界面"""
    demo_instance = ShortTermMemoryDemo()
    
    with gr.Blocks(title="短期记忆Demo", theme=gr.themes.Soft()) as demo:
        gr.Markdown("# 🧠 短期记忆演示")
        gr.Markdown("这个demo展示了如何实现对话历史记忆，AI能够记住之前的对话内容并基于历史进行回复。")
        
        with gr.Row():
            with gr.Column(scale=2):
                chatbot = gr.Chatbot(
                    label="💬 对话区域",
                    height=400,
                    show_label=True
                )
                
                with gr.Row():
                    msg = gr.Textbox(
                        label="输入消息",
                        placeholder="请输入您的问题...",
                        scale=4,
                        lines=2
                    )
                    send_btn = gr.Button("发送", scale=1, variant="primary")
                
                with gr.Row():
                    clear_btn = gr.Button("🗑️ 清除记忆", variant="secondary")
                    show_memory_btn = gr.Button("👁️ 查看记忆", variant="secondary")
            
            with gr.Column(scale=1):
                memory_display = gr.Textbox(
                    label="📋 记忆状态",
                    lines=15,
                    interactive=False,
                    placeholder="点击'查看记忆'按钮查看当前记忆内容"
                )
        
        gr.Markdown("""
        ### 💡 使用说明：
        1. **发送消息**: 在输入框中输入问题，AI会基于对话历史进行回复
        2. **查看记忆**: 点击"查看记忆"按钮查看当前存储的对话历史
        3. **清除记忆**: 点击"清除记忆"按钮重置对话，开始新的会话
        4. **记忆特点**: AI会记住本次会话中的所有对话内容
        """)
        
        # 事件绑定
        send_btn.click(
            demo_instance.chat,
            inputs=[msg, chatbot],
            outputs=[msg, chatbot]
        )
        
        msg.submit(
            demo_instance.chat,
            inputs=[msg, chatbot],
            outputs=[msg, chatbot]
        )
        
        clear_btn.click(
            demo_instance.clear_memory,
            outputs=[chatbot, memory_display]
        )
        
        show_memory_btn.click(
            demo_instance.show_memory,
            outputs=[memory_display]
        )
    
    return demo

if __name__ == "__main__":
    demo = create_demo()
    demo.launch(
        server_name="0.0.0.0",
        server_port=7861,
        share=False,
        show_error=True
    )
