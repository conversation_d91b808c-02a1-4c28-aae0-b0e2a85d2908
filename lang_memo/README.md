# LangChain 记忆系统演示

这个项目包含了3个LangChain记忆系统的演示，展示了短期记忆、长期记忆以及两者结合的使用方法。

## 项目结构

```
lang_memo/
├── .env                           # 环境配置文件
├── requirements.txt               # Python依赖
├── README.md                     # 说明文档
├── run_demos.py                  # 启动脚本
├── demo1_short_term_memory.py    # Demo 1: 短期记忆演示
├── demo2_long_term_memory.py     # Demo 2: 长期记忆演示
└── demo3_combined_memory.py      # Demo 3: 长短期记忆结合演示
```

## 环境配置

确保 `.env` 文件包含以下配置：
```
api_key=your_api_key
base_url=your_base_url
model=your_model_name
```

## 安装依赖

```bash
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple
```

## 演示说明

### Demo 1: 短期记忆演示 (demo1_short_term_memory.py)
- **功能**: 使用 `ConversationBufferMemory` 实现对话历史记忆
- **特点**: 
  - 保持对话上下文
  - 可以查看当前记忆内容
  - 可以清除记忆重新开始
- **端口**: 7861
- **适用场景**: 单次会话的连续对话

### Demo 2: 长期记忆演示 (demo2_long_term_memory.py)
- **功能**: 使用向量数据库(Chroma)实现持久化记忆存储
- **特点**:
  - 可以添加和分类记忆内容
  - 支持语义搜索查询
  - 记忆持久化存储
  - 可以管理记忆内容
- **端口**: 7862
- **适用场景**: 知识库构建、长期信息存储

### Demo 3: 长短期记忆结合演示 (demo3_combined_memory.py)
- **功能**: 结合 `ConversationBufferMemory` 和向量数据库
- **特点**:
  - 智能对话结合历史上下文和知识库
  - 可以将对话保存到长期记忆
  - 分别管理短期和长期记忆
  - 显示记忆状态统计
- **端口**: 7863
- **适用场景**: 智能助手、个人知识管理系统

## 运行方式

### 方式1: 使用启动脚本
```bash
python run_demos.py
```
然后选择要运行的演示。

### 方式2: 单独运行
```bash
# 运行短期记忆演示
python demo1_short_term_memory.py

# 运行长期记忆演示
python demo2_long_term_memory.py

# 运行长短期记忆结合演示
python demo3_combined_memory.py
```

## 使用指南

### Demo 1 使用步骤:
1. 启动后访问 http://localhost:7861
2. 在输入框中输入问题进行对话
3. 观察AI如何记住之前的对话内容
4. 使用"查看记忆"按钮查看当前记忆状态
5. 使用"清除记忆"按钮重置对话

### Demo 2 使用步骤:
1. 启动后访问 http://localhost:7862
2. 在"添加记忆"标签页中添加知识内容
3. 在"查询记忆"标签页中搜索相关信息
4. 在"记忆管理"标签页中管理所有记忆

### Demo 3 使用步骤:
1. 启动后访问 http://localhost:7863
2. 先在"长期记忆管理"中添加一些知识
3. 在"智能对话"中进行对话，观察AI如何结合短期对话历史和长期知识
4. 使用"保存对话到长期记忆"将有价值的对话保存
5. 使用"刷新记忆状态"查看记忆统计

## 技术特点

- **短期记忆**: 使用LangChain的ConversationBufferMemory
- **长期记忆**: 使用Chroma向量数据库进行语义搜索
- **嵌入模型**: 使用OpenAI兼容的嵌入模型
- **前端界面**: 使用Gradio构建交互式Web界面
- **记忆持久化**: 向量数据库数据持久化存储

## 注意事项

1. 首次运行长期记忆相关功能时，会自动创建向量数据库
2. 向量数据库文件存储在 `./chroma_db` 和 `./chroma_db_combined` 目录中
3. 确保有足够的磁盘空间存储向量数据
4. 如果遇到编码问题，确保系统支持UTF-8编码

## 扩展建议

- 可以添加更多记忆类型（如ConversationSummaryMemory）
- 可以集成更多向量数据库（如Pinecone、Weaviate）
- 可以添加记忆的自动分类和标签功能
- 可以实现记忆的导入导出功能
