import json
import os
import requests
from dotenv import load_dotenv

TOKEN_URL = "https://aip.baidubce.com/oauth/2.0/token"
ACCESS_URL = 'https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/completions_pro?access_token='
class YYLLM:
    def __init__(self):
        # 获得access_token
        self.token = None
        self.token = self.get_token()

    def get_token(self):
        if not self.token:
            # 加载.env文件
            load_dotenv()
            params = {
                "grant_type": "client_credentials",
                "client_id": os.getenv('ACCESS_kEY'),
                "client_secret": os.getenv('SECRET_kEY'),
            }
            # 远程获取token
            self.token = requests.post(TOKEN_URL, params=params).json()['access_token']
        return self.token

    def chat(self,text):
        headers = {
            'Content-Type': 'application/json',
        }
        data = json.dumps({"messages":[{"role":"user","content":text}]})
        # 远程调用文心一言chatapi
        return requests.post(ACCESS_URL + self.get_token(),data=data,headers=headers).json()['result']


if __name__ == '__main__':
    llm = YYLLM()
    print(llm.chat('量子计算机对于大模型有什么作用'))