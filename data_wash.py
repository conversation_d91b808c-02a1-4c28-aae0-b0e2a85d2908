import numpy as np
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.linear_model import LogisticRegression
from sklearn.model_selection import train_test_split


def generate_dummy_data(high_quality_size, low_quality_size):
    # 生成高质量数据，这里简单假设高质量数据是一些长句子
    high_quality_data = [
        "This is a high-quality sentence with rich information. " * np.random.randint(3, 10) for _ in range(high_quality_size)
    ]
    # 生成低质量数据，这里简单假设低质量数据是一些短句子
    low_quality_data = [
        "Short sentence." * np.random.randint(1, 3) for _ in range(low_quality_size)
    ]
    # 组合成数据集，并创建标签
    data = high_quality_data + low_quality_data
    labels = [1] * len(high_quality_data) + [0] * len(low_quality_data)
    return data, labels

def train_binary_classifier(data, labels):
    # 使用 TF-IDF 向量化文本数据
    vectorizer = TfidfVectorizer()
    X = vectorizer.fit_transform(data)
    # 打印词汇表
    print("Vocabulary:")
    print(vectorizer.vocabulary_)

    # 打印特征名称
    print("\nFeature Names:")
    print(vectorizer.get_feature_names_out())

    # 划分训练集和测试集
    X_train, X_test, y_train, y_test = train_test_split(
        X, labels, test_size=0.2, random_state=42)
    # 使用逻辑回归作为二元分类器
    classifier = LogisticRegression()
    classifier.fit(X_train, y_train)
    # 评估分类器性能
    accuracy = classifier.score(X_test, y_test)
    print(f"Classifier accuracy: {accuracy}")
    return classifier, vectorizer

def filter_low_quality_data(classifier, vectorizer, candidate_data):
    # 向量化候选数据
    X_candidate = vectorizer.transform(candidate_data)
    # 预测候选数据的质量
    predictions = classifier.predict(X_candidate)
    print("predictions")
    print(type(predictions))
    # 过滤低质量数据
    filtered_data = [
        candidate_data[i] for i in range(len(candidate_data)) if predictions[i] == 1
    ]
    return filtered_data
# 假设的候选数据
candidate_data = [
    "This is a relatively long and informative sentence.",
    "Short and simple sentence.",
    "Another long and meaningful statement.",
    "Short one."
]
if __name__ == '__main__':
    data, labels = generate_dummy_data(100, 100)
    classifier,vectorizer = train_binary_classifier(data, labels)
    filtered_data = filter_low_quality_data(classifier, vectorizer, candidate_data)
    print("\nFiltered data:")
    for data in filtered_data:
        print(data)