import base64

import openai
from dashscope import Generation

def chat_with_deepseek(user_question: str = None,msg: list[dict[str,str]] = None) -> str:
    """
    通过 Deepseek API 与 Deepseek 模型进行对话

    Args:
        user_question (str): 用户输入的问题

    Returns:
        str: 模型的回答或错误信息
        :param msg:
    """
    # 初始化 OpenAI 客户端，设置 Deepseek API 地址和密钥
    client = openai.OpenAI(base_url="https://api.deepseek.com/v1",api_key="***********************************")

    try:
        # 调用聊天完成接口
        response = client.chat.completions.create(
            model="deepseek-chat",  # 使用 deepseek-chat 模型
            messages=[
                {"role": "user", "content": user_question}  # 设置用户问题
            ] if msg is None else msg
        )
        # 提取回答内容
        answer = response.choices[0].message.content
        return answer
    except Exception as e:
        # 发生异常时返回错误信息
        return f"请求失败：{e}"

def chat_with_qwq(user_question: str = None,msg: list[dict[str,str]] = None) -> str:
    """
    通过 DashScope API 与 QWQ-32B 模型进行对话

    Args:
        user_question (str): 用户输入的问题

    Returns:
        str: 模型的回答
    """
    # 构造消息列表
    messages = [
        {'role': 'user', 'content': user_question}
    ] if msg is None else msg

    # 调用 Generation API，设置流式输出
    responses = Generation.call(
        api_key='sk-594ed6e5623849e68ea059bff1f14f56',  # DashScope API 密钥
        model="qwq-32b",  # 使用 QWQ-32B 模型
        messages=messages,
        result_format="message",  # 设置结果格式为消息格式
        stream=True,  # 启用流式输出
        incremental_output=True,  # 启用增量输出
    )

    # 合并所有流式响应的内容并返回
    return ''.join(res.output.choices[0].message.content for res in responses)

def chat_with_bailian_image(user_question: str = None, msg: list[dict[str, str]] = None, image: bytes = None) -> str:
    """
    通过 DashScope API 与百炼平台支持图片识别的最优大模型进行对话

    Args:
        user_question (str): 用户输入的问题
        msg (list[dict[str, str]]): 消息列表
        image (bytes): 输入的图片数据

    Returns:
        str: 模型的回答
    """
    # 构造消息列表
    messages = [
        {'role': 'user', 'content': user_question}
    ] if msg is None else msg

    # 调用 Generation API，设置流式输出
    responses = Generation.call(
        api_key='sk-594ed6e5623849e68ea059bff1f14f56',  # DashScope API 密钥
        model="bailian-v1",  # 使用百炼平台支持图片识别的最优大模型
        customized_model_id="bailian-v1",
        messages=messages,
        image=base64.b64encode(image),  # 添加图片参数
        result_format="message",  # 设置结果格式为消息格式
    )

    # 合并所有流式响应的内容并返回
    return responses.output.choices[0].message.content
import os
def find_image_with_keywords(directory: str) -> str:
    """
    遍历指定目录中的所有图片，调用 chat_with_bailian_image 检查图片中是否包含指定关键词。

    Args:
        directory (str): 图片所在的目录路径
        keywords (list[str]): 要检查的关键词列表

    Returns:
        str: 包含关键词的图片文件名，如果未找到则返回空字符串
    """
    for filename in os.listdir(directory):
        if filename.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp', '.gif')):
            image_path = os.path.join(directory, filename)
            print(image_path)
            with open(image_path, 'rb') as img_file:
                image_data = img_file.read()
                response = chat_with_bailian_image(
                    user_question="图片中是否包含穿搭,穿着搭配等类似的词汇",
                    image=image_data
                )
                print(response)
                print("*"*100)
    return ""


msg = [{'content':"""You are in a role play game. The following roles are available:
                market: 提供市场分析方法,提供预测未来可能流行的商品的方法
pm: 产品经理
user_proxy: 用户代理,执行coder编写的代码,并将结果返回给coder进行分析.
coder: 程序员,代码编写后交给user_proxy执行,并分析返回结果,如果返回结果符合预期,根据任务要求可以继续编写代码,或将结果交给pm,market分析,或返回给user_proxy
                Read the following conversation.
                Then select the next role from ['market', 'pm', 'user_proxy', 'coder'] to play. Only return the role.""",'role': 'system'},{
    'content': '请在arxiv网站上找到最新的关于gpt4的论文，并找出这篇论文在python软件领域潜在的应用', 'name': 'user_proxy', 'role': 'user'
},{
    'content': r"""# filename: fetch_arxiv_gpt4.py
    ```python
import requests
import feedparser
from datetime import datetime, timedelta

def fetch_latest_gpt4_paper():
    # Search arXiv for GPT-4 related papers from last 30 days
    base_url = "http://export.arxiv.org/api/query?"
    query = "all:GPT-4+OR+all:GPT4"
    params = {
        "search_query": query,
        "sortBy": "submittedDate",
        "sortOrder": "descending",
        "max_results": 5
    }

    response = requests.get(base_url, params=params)
    feed = feedparser.parse(response.text)

    latest_paper = None
    for entry in feed.entries:
        if 'GPT-4' in entry.title or 'GPT4' in entry.title:
            published = datetime.strptime(entry.published, '%Y-%m-%dT%H:%M:%SZ')
            if datetime.now() - published < timedelta(days=30):
                latest_paper = {
                    'title': entry.title,
                    'authors': ', '.join(author.name for author in entry.authors),
                    'published': entry.published,
                    'summary': entry.summary,
                    'pdf_url': [link.href for link in entry.links if link.type == 'application/pdf'][0],
                    'arxiv_url': entry.id
                }
                break

    return latest_paper

def analyze_python_applications(paper_summary):
    # Analyze potential Python applications based on paper content
    python_applications = []

    if 'fine-tuning' in paper_summary.lower():
        python_applications.append("Creating Python libraries for GPT-4 fine-tuning")

    if 'API' in paper_summary or 'interface' in paper_summary:
        python_applications.append("Developing Python SDKs for GPT-4 API integration")

    if 'evaluation' in paper_summary.lower() or 'benchmark' in paper_summary.lower():
        python_applications.append("Building Python-based evaluation frameworks for GPT-4 performance")

    if 'multimodal' in paper_summary.lower():
        python_applications.append("Creating Python tools for GPT-4's multimodal capabilities (image+text processing)")

    if 'plugin' in paper_summary.lower() or 'extension' in paper_summary.lower():
        python_applications.append("Developing Python plugins/extensions for GPT-4 ecosystem")

    return python_applications if python_applications else ["General Python integration with GPT-4 APIs"]

if __name__ == "__main__":
    paper = fetch_latest_gpt4_paper()
    if paper:
        print("Latest GPT-4 related paper found:")
        print(f"Title: {paper['title']}")
        print(f"Authors: <AUTHORS>
        print(f"Published: {paper['published']}")
        print(f"Summary: {paper['summary'][:500]}...")  # Show first 500 chars
        print(f"PDF URL: {paper['pdf_url']}")
        print("\nPotential Python applications:")
        applications = analyze_python_applications(paper['summary'])
        for i, app in enumerate(applications, 1):
            print(f"{i}. {app}")
    else:
        print("No recent GPT-4 papers found in the last 30 days")
```""", 'name': 'coder', 'role': 'user'
},{'content': "Read the above conversation. Then select the next role from ['market', 'pm', 'user_proxy', 'coder'] to play. Only return the role.", 'name': 'checking_agent', 'role': 'system'}]

if __name__ == "__main__":
    # print( chat_with_deepseek(msg=msg))
    print(find_image_with_keywords("F:\日报"))