from functools import lru_cache

from openai import OpenAI

# 定义一个基础的模型类 BaseLLM
class BaseLLM:
    # 使用 lru_cache 装饰器，设置缓存大小为 1024，缓存函数 chat 的返回值
    @lru_cache(maxsize=1024)
    def chat(self, text):
        # 调用内部函数 _chat 处理传入的文本
        return self._chat(text)

    # 定义一个内部函数 _chat，用于处理文本，需要在子类中实现具体逻辑
    def _chat(self, text):
        # 抛出 NotImplementedError 异常，提示子类需要实现该方法
        raise NotImplementedError
# 定义 QwenLLM 类，继承自 BaseLLM， 其接口与gpt系列基本相同
class QwenLLM(BaseLLM):
    def __init__(self):
        # 初始化 QwenLLM 实例，传入环境变量中的 api_key 和 base_url
        self.client = OpenAI(
            api_key="sk-8709f7ed33dc402a8a9885a1a8ee403e",
            base_url="https://dashscope.aliyuncs.com/compatible-mode/v1"
        )


    def chat(self, text, messages=[],stops=None):
        if not messages:
            # 如果没有历史消息，则将用户输入作为第一条消息
            messages = [{"role": "user", "content": text}]

        # 使用固定的 engine 发送对话请求，并获取回复结果
        response = self.client.chat.completions.create(
            model="qwen-turbo",
            messages=messages,
            temperature=0.8,
            top_p=0.8,
            stop=stops
        )

        # 返回回复内容
        return response.choices[0].message.content

qwen_bot = QwenLLM()


def div_func(a: float, b: float):
    """
    除法函数
    """
    return a / b

def mul_func(a: float, b: float):
    """
    乘法函数
    """
    return a * b

def chat_func(text: str):
    """
    聊天函数
    """
    return text


prompt_6 = """你可以调用下面列出的函数来辅助完成用户提出的问题，函数的描述如下
- div_func(a: float, b: float)：除法函数
- mul_func(a: float, b: float)：乘法函数
- chat_func(text: str)：聊天函数
你的输出必须满足如下的格式
动作: <你要调用的函数名称>
函数输入: <输入函数的值，必须符合函数的输入参数要求，如果有多个值每个值之间用逗号分隔>
观察: <函数的输出结果>
思考: 你对接下来的思考
你将会在观察中看到函数的输出结果，然后思考你的下一步行动，思考完后将进行下一轮动作和函数输入，每组之间不要重叠，如果已经找到了答案就执行 chat_func 函数

请问：1.5 除以 2.5 再乘以 3.5 是多少？直接告诉我答案
"""

def get_action_and_args(text: str):
    """
    解析输入的字符串，得到动作和参数
    """
    lines = [l for l in text.split("\n") if l.strip()]  # 将文本按行分割并去除空行
    action = ""  # 初始化动作为空字符串
    args = []  # 初始化参数列表为空
    for line in lines:  # 遍历每一行
        if line.startswith("动作:"):  # 如果行以"动作:"开头
            action = line.replace("动作:", "").strip()  # 提取动作内容并去除首尾空格
        elif line.startswith("函数输入:"):  # 如果行以"函数输入:"开头
            args = line.replace("函数输入:", "").strip().split(",")  # 提取参数内容并按逗号分割成列表
    return action, args  # 返回动作和参数列表

def tool_call(llm: QwenLLM, text: str):
    """
    调用工具函数，接受一个OpenAILLM实例和文本作为参数
    """
    output = ""  # 初始化输出为空字符串
    loop_count = 0  # 初始化循环计数器为0
    messages = [{"role": "user", "content": text}]  # 初始化消息列表，包含用户的输入文本
    while loop_count < 10:  # 循环开始，最多循环10次
        loop_count += 1  # 每次循环计数器加1
        llm_output = llm.chat(text, messages, stops='观察:')  # 调用llm的_chat方法，传入文本和消息列表
        print(llm_output.replace("\n\n", "\n"))  # 打印llm输出并替换连续的空行为单个空行
        messages += [{"role": "assistant", "content": llm_output}]  # 将助手返回的消息添加到消息列表中
        action, args = get_action_and_args(llm_output)  # 解析助手返回消息中的动作和参数
        if action and args:  # 如果存在动作和参数
            if action == "chat_func":  # 如果动作是"chat_func"
                output = chat_func(args[0])  # 调用chat_func函数并将结果赋给output
                break  # 跳出循环
            elif action == "div_func":  # 如果动作是"div_func"
                func_output = div_func(float(args[0]), float(args[1]))  # 调用div_func函数
            elif action == "mul_func":  # 如果动作是"mul_func"
                func_output = mul_func(float(args[0]), float(args[1]))  # 调用mul_func函数
            print(f"观察: {func_output}")  # 打印观察结果
            messages += [{"role": "user", "content": f"观察: {func_output}"}]  # 将观察结果添加到消息列表中
        else:  # 如果找不到对应的函数或参数传入错误
            messages += [{"role": "user", "content": "观察: 没有找到对应的函数或参数传入错误"}]  # 添加提示消息到消息列表
            continue  # 继续下一次循环

    return output  # 返回输出结果

if __name__ == '__main__':
    tool_call(qwen_bot, prompt_6)