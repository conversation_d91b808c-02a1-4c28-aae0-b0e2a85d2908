import gradio
from openai import OpenAI
import time
import logging
from typing import List, Dict, Optional, Any
import json

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 创建 OpenAI 客户端，配置 API 密钥和自定义 base_url
client = OpenAI(
    api_key="sk-8709f7ed33dc402a8a9885a1a8ee403e",
    base_url="https://dashscope.aliyuncs.com/compatible-mode/v1"
)

# 测试用例
TEST_CASES = [
    {
        "name": "基础问答测试",
        "input": "你好，请介绍一下你自己",
        "expected_keywords": ["你好", "我是", "助手"]
    },
    {
        "name": "专业知识测试",
        "input": "请解释一下Python中的装饰器是什么？",
        "expected_keywords": ["装饰器", "函数", "Python"]
    },
    {
        "name": "多轮对话测试",
        "input": "我们来玩个游戏，我说一个词，你接下一个词",
        "expected_keywords": ["游戏", "接词"]
    },
    {
        "name": "边界条件测试",
        "input": "a" * 1000,  # 超长输入
        "expected_keywords": []
    }
]

def run_test_cases() -> Dict[str, Any]:
    """运行测试用例并返回结果"""
    results = {}
    for test in TEST_CASES:
        try:
            start_time = time.time()
            response = chat(test["input"], [])
            end_time = time.time()
            
            # 检查响应时间
            response_time = end_time - start_time
            time_check = response_time < 5.0  # 假设5秒为可接受的最大响应时间
            
            # 检查关键词
            keyword_check = all(keyword in response for keyword in test["expected_keywords"])
            
            results[test["name"]] = {
                "passed": time_check and keyword_check,
                "response_time": response_time,
                "response": response
            }
            
            logger.info(f"测试用例 '{test['name']}' 完成，响应时间: {response_time:.2f}秒")
        except Exception as e:
            logger.error(f"测试用例 '{test['name']}' 失败: {str(e)}")
            results[test["name"]] = {
                "passed": False,
                "error": str(e)
            }
    return results


sys_msg = """
你将协助一位准备面试AI应用工程师的人完善他所编造的项目。你的任务是与他讨论该项目，筛选出合理的功能，探讨方案及细节实现，并进行模拟面试，确保面试时项目不会穿帮。
首先，请仔细阅读以下项目功能描述以及技术栈,设计实施方案的描述：
<项目功能描述>
{{PROJECT_FUNCTIONS}}
</项目功能描述>
<技术栈描述>
python,dify,后期转向langgraph,redis,milvus,minerU,bge-m3
</技术栈描述>
<设计实施方案描述>
应对冷启动:1.意图识别: 不做意图识别,前端多个菜单项,后端多个接口拆分不同意图.
意图识别采用bert-chinese-base多级意图识别
bge-m3做嵌入
qwen2.5-instruct-7b做对话
</设计实施方案描述>
### 筛选合理功能
在<思考>标签中详细分析每个功能的合理性，考虑技术可行性、市场需求、与AI应用工程师岗位的相关性等因素。然后在<筛选结果>标签中列出你认为合理的功能。例如：
<思考>
[在此详细说明你对每个功能合理性的分析]
</思考>
<筛选结果>
[列出合理的功能]
</筛选结果>

### 讨论方案及细节实现
对于筛选出的合理功能，在<思考>标签中详细探讨实现这些功能的方案及细节，如使用的算法、技术框架、数据处理方式等。然后在<方案及细节>标签中总结讨论结果。例如：
<思考>
[在此详细说明你对每个功能实现方案及细节的讨论]
</思考>
<方案及细节>
[总结讨论结果]
</方案及细节>

### 模拟面试
接下来进行模拟面试。假设你是面试官，在<面试问题>标签中提出至少5个与项目相关的问题，这些问题应涵盖项目功能、实现方案、技术细节、遇到的挑战及解决方案等方面。在<回答示例>标签中针对每个问题给出一个详细、合理的回答示例。例如：
<面试问题>
[问题1]
[问题2]
...
</面试问题>
<回答示例>
[针对问题1的回答示例]
[针对问题2的回答示例]
...
</回答示例>

请按照上述要求进行回复，确保回答丰富、全面。

"""

def chat(text: str, history: List[Dict[str, str]], 
         temperature: float = 0.7, 
         max_tokens: Optional[int] = None) -> str:
    """
    聊天函数，接收用户输入和历史消息，返回模型回复
    
    参数:
        text: 用户输入的文本
        history: 历史消息列表
        temperature: 温度参数，控制输出的随机性 (0.0-1.0)
        max_tokens: 最大输出token数
    """
    proj = """
    功能1: 政策查询(如员工手册),使用上下文检索第一阶段通过多轮对话澄清+LLM总结生成精准查询语句；第二阶段采用混合检索策略（当前问题向量+历史相似问答对重排），实现召回率90%、F1值85%
    功能2: 流程查询如(报销,离职等手续), 基于minerU实现pdf-markdown转换构建分层检索系统，利用Markdown多级标题分块并建立标题-正文ID映射，通过动态阈值匹配标题后检索关联正文，结合局部重排序与跨分块语义扩展，召回率对比重叠分块检索提升8%
    功能3: 错误诊断与错误记录,:采用结构化+向量化混合检索架构：错误代码,错误类型通过Milvus标量过滤精准匹配；非结构化字段（原因/解决方案）使用向量检索,保存时根据各字段相似度与权重判断是否存在重复.
    功能5: 构建轻量级供应商评价系统：每日凌晨执行批量离线推理，生成结构化摘要。摘要内容强制包含交付质量/服务响应/成本效益三级评估
    功能6: 双优化查询,根据施工方案,项目信息,根据项目信息与施工方案摘要相似度匹配,采用滑动窗口分块策略处理施工方案，通过链式摘要生成确保整体连贯性。基于Milvus构建项目信息与施工摘要的混合向量库，使用余弦相似度匹配Top5案例。
    功能7: 构建轻量级个人年度开发报告系统,通过Gitee API拉取commit日志，采用多阶段清洗过滤无效提交（合并提交/无信息commit）,生成结构化自然语言总结
    功能8: 公文改写,支持邮件,基于mengzi-t5纠错模型进行错字纠正,Qwen2.5实现精细化风格转换,bge-m3验证保障语义一致性
    """
    try:
        msg = [{"role": "system", "content": sys_msg.replace("{{PROJECT_FUNCTIONS}}", proj)}]
        start_time = time.time()
        msg.extend(history.copy())
        msg.append({"role": "user", "content": text})
        
        # 添加性能优化的参数
        response = client.chat.completions.create(
            model="qwen3-235b-a22b",
            messages=msg,
            extra_body={
                # 开启深度思考的参数，对 QwQ 模型无效
                "enable_thinking": True,
                "enable_search": True,  # 开启联网搜索的参数
                "search_options": {
                    "forced_search": True,  # 强制联网搜索的参数
                    "search_strategy": "pro",  # 模型将搜索10条互联网信息
                }
            },
            stream=True
        )
        
        end_time = time.time()
        logger.info(f"请求处理时间: {end_time - start_time:.2f}秒")
        
        # 处理流式响应
        content = ""
        for chunk in response:
            if hasattr(chunk, "choices") and chunk.choices and hasattr(chunk.choices[0], "delta"):
                delta = chunk.choices[0].delta
                if hasattr(delta, "content") and delta.content:
                    content += delta.content

        return content
    except Exception as e:
        logger.error(f"聊天处理出错: {str(e)}")
        return f"抱歉，处理您的请求时出现错误: {str(e)}"

def save_test_results(results: Dict[str, bool], filename: str = "test_results.json"):
    """保存测试结果到文件"""
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)

# 创建 Gradio 聊天界面并启动
iframe = gradio.ChatInterface(
    fn=chat,
    type="messages",
    title="Chat bot",
    description="聊天机器人界面",
    examples=[
        ["你好，请介绍一下你自己"],
        ["请解释一下Python中的装饰器是什么？"],
        ["我们来玩个游戏，我说一个词，你接下一个词"]
    ]
)

# 运行测试用例
if __name__ == "__main__":
    iframe.launch()
