# 导入dotenv模块
import dotenv
import os

# 加载.env文件中的环境变量
dotenv.load_dotenv("../.env")
from langchain_openai import ChatOpenAI
from langchain_community.chat_models.tongyi import ChatTongyi
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.output_parsers import StrOutputParser
# 使用qwen-max模型进行聊天
qwen = ChatOpenAI(
    model="qwen-max",
    api_key="sk-8709f7ed33dc402a8a9885a1a8ee403e",
    openai_api_base="https://dashscope.aliyuncs.com/compatible-mode/v1"
)
qwen.invoke("你好")