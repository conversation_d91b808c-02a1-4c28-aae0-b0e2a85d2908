import base64
import io

import seaborn as sns

import gradio
import pandas as pd
from dotenv import load_dotenv
from langchain.agents import AgentExecutor
from langchain_community.agent_toolkits import create_sql_agent, SQLDatabaseToolkit
from langchain_community.tools import TavilySearchResults
from langchain_community.utilities import SQLDatabase
from langchain_core.prompts import PromptTemplate
from langchain_core.tools import tool
from langchain_openai import ChatOpenAI
from matplotlib import pyplot as plt
from pydantic import SecretStr
from sqlalchemy import create_engine

load_dotenv()

tavily_search = TavilySearchResults(max_results=2)


@tool
def generate_visualization(query: str, data: str = None) -> str:
    """生成数据可视化图表。

    根据用户提供的查询参数和数据生成相应的数据可视化图表。支持多种图表类型，
    并自动处理数据格式化和图表样式。

    Args:
        query (str): 查询字符串，格式为 "列名,图表类型"。
            列名: 要可视化的数据列名
            图表类型: 可选值包括 'line'（折线图）, 'bar'（柱状图）,
                     'hist'（直方图）, 'box'（箱线图）
            示例: "GDP_rate,bar" 或 "country,hist"
        data (str, optional): JSON格式的数据字符串。如果提供，将使用此数据而不是全局DataFrame。
            格式示例: '{"year": [1960, 1961], "country": ["US", "UK"], "GDP_rate": [100, 200]}'

    Returns:
        str: 返回图表生成的状态信息，成功时返回 "生成图表成功"。

    Raises:
        Exception: 当图表生成过程中出现错误时抛出异常，包含具体的错误信息。

    Examples:
        >>> generate_visualization("GDP_rate,bar", '{"year": [1960], "GDP_rate": [100]}')
        '生成图表成功'
        
        >>> generate_visualization("country,hist")
        '生成图表成功'

    Notes:
        - 图表默认大小为 10x6 英寸
        - 自动添加标题和调整布局
        - 支持的数据类型包括数值型和分类型
        - 图表生成后会自动关闭，释放内存
        - 如果提供data参数，将优先使用data中的数据
    """
    try:
        # 解析查询中的列名和图表类型
        columns = query.split(',')
        column = columns[0].strip()
        chart_type = columns[1].strip() if len(columns) > 1 else 'line'
        
        # 获取数据
        if data:
            # 如果提供了JSON数据，使用JSON数据
            import json
            df = pd.DataFrame(json.loads(data))
        else:
            # 否则使用全局DataFrame
            df = processor.df
            
        if df is None:
            return "错误：没有可用的数据"

        plt.figure(figsize=(10, 6))
        if chart_type == 'line':
            df[column].plot(kind='line')
        elif chart_type == 'bar':
            df[column].value_counts().plot(kind='bar')
        elif chart_type == 'hist':
            sns.histplot(data=df, x=column)
        elif chart_type == 'box':
            sns.boxplot(data=df, y=column)

        plt.title(f'{column}的{chart_type}图')
        plt.tight_layout()
        processor.plt = plt

        return "生成图表成功"
    except json.JSONDecodeError:
        return "错误：JSON数据格式不正确"
    except Exception as e:
        return f"生成图表时出错: {str(e)}"


class CSVProcessor:
    def __init__(self):
        self.file = None
        self.engine = create_engine('sqlite:///csv.db')
        self.db = SQLDatabase(self.engine)
        self.agent = None
        self.df = None
        self.plt = None
        self.llm = ChatOpenAI(api_key=SecretStr("sk-8709f7ed33dc402a8a9885a1a8ee403e"),
                              base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
                              model="qwen3-32b",
                              streaming=True
                              )



    def load(self, file):
        if file:
            self.file = file
            # 将CSV文件加载到SQLite数据库中
            self.df = pd.read_csv(file)
            self.df.to_sql('csv_data', self.engine, if_exists='replace', index=False)
            self.db = SQLDatabase(self.engine)
            print("CSV文件已加载到数据库中。")
            prefix = '''
            你是一个智能数据分析助手，具备以下能力：
            1. 数据库分析能力：
               - 根据用户输入的问题，生成语法正确的{dialect}查询语句并执行
               - 查询结果限制在最多{top_k}条
               - 可以根据相关列对结果进行排序，返回最具代表性的示例
               - 仅针对问题需求选择相关列，避免查询所有列
               - 禁止对数据库进行任何DML操作（如INSERT、UPDATE、DELETE、DROP等）

            2. 数据可视化能力：
               - 支持生成多种类型的图表（折线图、柱状图、直方图、箱线图等）
               - 可以根据数据特征自动选择合适的图表类型
               - 提供图表的解释和分析

            3. 外部知识补充能力：
               - 当数据库中的信息不足以回答问题时，可以使用外部搜索工具获取补充信息
               - 将外部知识与数据库分析结果结合，提供更全面的答案
               - 确保外部信息的准确性和相关性
            我可以查询的表有:{table_names},我可以从{table_info}中了解到表结构。
            在执行查询前，务必仔细检查语句。若执行过程中遇到错误，请重写查询并再次尝试。
            
            若问题与数据分析和处理完全无关，直接回答"这个问题超出了我的能力范围"。
            '''
            prefix = PromptTemplate.from_template(prefix,partial_variables={"table_names":self.db.get_table_names(),"table_info":self.db.get_table_info()})
            suffix = '''
            我可以执行以下操作：
            1. 数据库查询和分析
            2. 数据可视化（使用generate_visualization工具）
            3. 外部知识搜索（使用tavily_search工具）
            
            我会根据用户的问题，选择最合适的工具组合来完成任务。
            '''
            self.agent: AgentExecutor = create_sql_agent(self.llm, prefix=prefix, suffix=suffix, db=self.db,
                                                         agent_type="tool-calling", verbose=True, max_iterations=60,
                                                         top_k=30, extra_tools=[tavily_search,generate_visualization], )

    def process(self, text: str):
        if not self.agent:
            return "请先上传CSV文件。"
        try:
            self.plt = None
            res = self.agent.invoke({
                "input": text})
            return res["output"]
        except Exception as e:
            raise e


processor = CSVProcessor()


def chat(text, file, history):
    print(file)
    history = history + [{"role": "user", "content": text}]
    if file:
        processor.load(file)
    if text:
        res = processor.process(text)
        return history + [{"role": "assistant", "content": res}], processor.plt
    else:
        return history, processor.plt


# 创建Gradio界面
with gradio.Blocks(title="CSV助手") as iframe:
    with gradio.Row(equal_height=True):
        # 创建聊天界面
        with gradio.Column(scale=1, min_width=1):
            chatbot = gradio.Chatbot(height=600, type='messages')
            with gradio.Row(equal_height=True):
                # 文本输入框
                text_input = gradio.Textbox(
                    placeholder="在这里输入文本...",
                    label="文本输入",
                    lines=5,
                    min_width=1,
                    scale=3
                )
                # 文件上传组件
                file_input = gradio.File(
                    label="上传csv文件",
                    file_types=[".csv"],
                    height=110,
                    min_width=1,
                    scale=1
                )
                # 处理文件上传事件
                # def on_file_upload(file):
                #     # 清除gradio缓存文件
                #     gradio.clear_cache()
                #     return file
                #
                # # 绑定文件上传事件
                # file_input.upload(fn=on_file_upload, inputs=file_input, outputs=file_input)

            # 提交按钮
            submit_btn = gradio.Button("发送")

        plot = gradio.Plot(scale=1)

    # 处理提交事件
    submit_btn.click(
        fn=chat,
        inputs=[text_input, file_input, chatbot],
        outputs=[chatbot, plot]
    )

# 启动应用
iframe.launch()
