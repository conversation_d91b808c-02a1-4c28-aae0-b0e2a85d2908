import collections
import re

text = ('The establishment of wildlife corridors and protected areas is a pivotal strategy in animal conservation, aiming to connect fragmented habitats, '
        'facilitate gene flow, and ensure the long-term survival of endangered species amidst the ongoing threat of habitat loss and climate change')

def get_vocab():
    vocab = collections.defaultdict(int)
    for word in text.strip().split():
        vocab[' '.join(word)+'</w>'] += 1
    return vocab

def get_state(vocab):
    pairs = collections.defaultdict(int)
    for word, freq in vocab.items():
        letters = word.split()
        for i in range(len(letters) - 1):
            pairs[letters[i], letters[i + 1]] += freq
    return pairs

def merge_vocab(pair, vocab):
    v_out = collections.defaultdict(int)
    ass = re.escape(' '.join(pair))
    p = re.compile(r'(?<!\S)' + ass + r'(?!\S)')
    for word, freq in vocab.items():
        new = p.sub(''.join(pair),word)
        v_out[new] = vocab[word]
    return v_out

def get_tokens(vocab):
    tokens = collections.defaultdict(int)
    for word, freq in vocab.items():
        wt = word.split()
        for t in wt:
            tokens[t] += freq
    return tokens

if __name__ == '__main__':
    vocab = get_vocab()
    tokens = get_tokens(vocab)
    num_merges = 20
    for i in range(num_merges):
        pairs = get_state(vocab)
        if not pairs:
            break
        best = max(pairs, key = pairs.get)
        vocab = merge_vocab(best, vocab)
        tokens[''.join(best)] = pairs[best]

        tokens[best[0]] -= pairs[best]
        tokens[best[1]] -= pairs[best]
    print(tokens)

