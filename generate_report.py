from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.oxml.ns import qn
from docx.shared import RGBColor

def create_report():
    # 创建文档对象
    doc = Document()
    
    # 设置中文字体
    doc.styles['Normal'].font.name = '宋体'
    doc.styles['Normal']._element.rPr.rFonts.set(qn('w:eastAsia'), '宋体')
    
    # 标题
    title = doc.add_heading('智能CSV数据分析助手', level=0)
    title.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    # 1. 数据描述
    doc.add_heading('1. 数据描述', level=1)
    
    # 1.1 业务描述
    doc.add_heading('1.1 业务描述', level=2)
    p = doc.add_paragraph()
    p.add_run('本项目旨在开发一个智能CSV数据分析助手，该助手能够：\n').bold = True
    doc.add_paragraph('• 处理和分析CSV格式的数据文件', style='List Bullet')
    doc.add_paragraph('• 提供自然语言交互界面', style='List Bullet')
    doc.add_paragraph('• 支持数据可视化', style='List Bullet')
    doc.add_paragraph('• 结合外部知识进行深度分析', style='List Bullet')
    doc.add_paragraph('• 适用于个人研究、数据分析等场景', style='List Bullet')
    
    # 1.2 CSV文件描述
    doc.add_heading('1.2 CSV文件描述', level=2)
    p = doc.add_paragraph()
    p.add_run('示例数据文件包含以下字段：\n').bold = True
    doc.add_paragraph('• year: 年份（如1960）', style='List Bullet')
    doc.add_paragraph('• country: 国家名称（如美国、英国、法国等）', style='List Bullet')
    doc.add_paragraph('• GDP_rate: 国内生产总值（以美元为单位）', style='List Bullet')
    
    p = doc.add_paragraph()
    p.add_run('数据特点：\n').bold = True
    doc.add_paragraph('• 时间跨度：1960年', style='List Bullet')
    doc.add_paragraph('• 覆盖国家：包括主要发达国家和发展中国家', style='List Bullet')
    doc.add_paragraph('• 数据类型：包含数值型（GDP_rate）和分类型（country）数据', style='List Bullet')
    doc.add_paragraph('• 数据规模：包含多个国家的GDP数据', style='List Bullet')
    
    # 2. Agent选择以及应用构建
    doc.add_heading('2. Agent选择以及应用构建', level=1)
    
    # 2.1 Agent选择
    doc.add_heading('2.1 Agent选择', level=2)
    p = doc.add_paragraph()
    p.add_run('本项目选择了以下组件：\n').bold = True
    doc.add_paragraph('1. LangChain SQL Agent', style='List Number')
    doc.add_paragraph('   • 用于处理数据库查询和分析', style='List Bullet')
    doc.add_paragraph('   • 支持自然语言转SQL查询', style='List Bullet')
    doc.add_paragraph('   • 提供灵活的数据分析能力', style='List Bullet')
    
    doc.add_paragraph('2. 通义千问大模型（Qwen3-32b）', style='List Number')
    doc.add_paragraph('   • 作为Agent的核心语言模型', style='List Bullet')
    doc.add_paragraph('   • 提供强大的自然语言理解能力', style='List Bullet')
    doc.add_paragraph('   • 支持复杂的数据分析任务', style='List Bullet')
    
    doc.add_paragraph('3. Tavily Search', style='List Number')
    doc.add_paragraph('   • 提供外部知识补充', style='List Bullet')
    doc.add_paragraph('   • 增强数据分析的深度和广度', style='List Bullet')
    
    # 2.2 自定义工具描述
    doc.add_heading('2.2 自定义工具描述', level=2)
    p = doc.add_paragraph()
    p.add_run('1. 数据可视化工具（generate_visualization）\n').bold = True
    doc.add_paragraph('• 功能：生成多种类型的图表', style='List Bullet')
    doc.add_paragraph('• 支持的图表类型：', style='List Bullet')
    doc.add_paragraph('  - 折线图（line）', style='List Bullet')
    doc.add_paragraph('  - 柱状图（bar）', style='List Bullet')
    doc.add_paragraph('  - 直方图（hist）', style='List Bullet')
    doc.add_paragraph('  - 箱线图（box）', style='List Bullet')
    
    p = doc.add_paragraph()
    p.add_run('2. 外部搜索工具（Tavily Search）\n').bold = True
    doc.add_paragraph('• 功能：补充外部知识', style='List Bullet')
    doc.add_paragraph('• 使用场景：', style='List Bullet')
    doc.add_paragraph('  - 历史背景分析', style='List Bullet')
    doc.add_paragraph('  - 经济趋势解释', style='List Bullet')
    doc.add_paragraph('  - 数据相关性分析', style='List Bullet')
    
    # 2.3 应用描述
    doc.add_heading('2.3 应用描述', level=2)
    p = doc.add_paragraph()
    p.add_run('1. 技术架构\n').bold = True
    doc.add_paragraph('• 前端：Gradio', style='List Bullet')
    doc.add_paragraph('• 后端：Python', style='List Bullet')
    doc.add_paragraph('• 数据库：SQLite', style='List Bullet')
    doc.add_paragraph('• 核心框架：LangChain', style='List Bullet')
    
    p = doc.add_paragraph()
    p.add_run('2. 主要功能\n').bold = True
    doc.add_paragraph('• 数据上传和加载', style='List Bullet')
    doc.add_paragraph('• 自然语言交互', style='List Bullet')
    doc.add_paragraph('• 数据可视化', style='List Bullet')
    doc.add_paragraph('• 智能分析', style='List Bullet')
    doc.add_paragraph('• 外部知识补充', style='List Bullet')
    
    # 3. 总结
    doc.add_heading('3. 总结', level=1)
    
    # 3.1 应用功能展示
    doc.add_heading('3.1 应用功能展示', level=2)
    doc.add_paragraph('[此处需要插入应用界面的截图]')
    
    # 3.2 作业感想
    doc.add_heading('3.2 作业感想', level=2)
    p = doc.add_paragraph()
    p.add_run('1. 技术收获\n').bold = True
    doc.add_paragraph('• 深入理解了LangChain框架的应用', style='List Bullet')
    doc.add_paragraph('• 掌握了Agent系统的构建方法', style='List Bullet')
    doc.add_paragraph('• 提升了数据可视化能力', style='List Bullet')
    doc.add_paragraph('• 学会了如何结合大模型进行数据分析', style='List Bullet')
    
    p = doc.add_paragraph()
    p.add_run('2. 项目挑战\n').bold = True
    doc.add_paragraph('• Agent工具集成的复杂性', style='List Bullet')
    doc.add_paragraph('• 数据可视化的动态更新', style='List Bullet')
    doc.add_paragraph('• 外部知识的有效整合', style='List Bullet')
    
    p = doc.add_paragraph()
    p.add_run('3. 改进方向\n').bold = True
    doc.add_paragraph('• 增加更多数据分析工具', style='List Bullet')
    doc.add_paragraph('• 优化图表展示效果', style='List Bullet')
    doc.add_paragraph('• 提升响应速度', style='List Bullet')
    doc.add_paragraph('• 增强错误处理能力', style='List Bullet')
    
    # 3.3 课程反馈
    doc.add_heading('3.3 课程反馈', level=2)
    p = doc.add_paragraph()
    p.add_run('1. 课程内容\n').bold = True
    doc.add_paragraph('• 理论与实践结合紧密', style='List Bullet')
    doc.add_paragraph('• 案例丰富且实用', style='List Bullet')
    doc.add_paragraph('• 技术栈选择合理', style='List Bullet')
    
    p = doc.add_paragraph()
    p.add_run('2. 学习建议\n').bold = True
    doc.add_paragraph('• 增加更多实战案例', style='List Bullet')
    doc.add_paragraph('• 提供更详细的文档', style='List Bullet')
    doc.add_paragraph('• 加强代码审查环节', style='List Bullet')
    
    p = doc.add_paragraph()
    p.add_run('3. 改进建议\n').bold = True
    doc.add_paragraph('• 增加更多工具使用示例', style='List Bullet')
    doc.add_paragraph('• 提供更多调试技巧', style='List Bullet')
    doc.add_paragraph('• 加强项目架构设计指导', style='List Bullet')
    
    # 保存文档
    doc.save('智能CSV数据分析助手报告.docx')

if __name__ == '__main__':
    create_report() 