import math


def perplexity(sentence, language_model):
    """
    基于度量的过滤：使用困惑度检测不自然句子
    :param sentence: 要计算困惑度的句子
    :param language_model: 语言模型（这里假设是一个简单的概率分布）
    :return: 句子的困惑度
    """
    tokens = sentence.split()
    N = len(tokens)
    log_prob_sum = 0
    for i in range(1, N):
        bigram = (tokens[i - 1], tokens[i])
        if bigram in language_model:
            log_prob_sum += math.log(language_model[bigram])
        else:
            log_prob_sum += math.log(1e-10)  # 避免 log(0)
    perplexity_value = math.exp((-1 / N) * log_prob_sum)
    return perplexity_value
language_model = {
    ("This", "is"): 0.2,
    ("is", "a"): 0.3,
    ("a", "relevant"): 0.1
}
texts = [
    "This is a relevant text for the task. It contains useful information.",
    "This is an irrelevant text with some noise like <html> and http://example.com",
    "A short sentence.",
    "Another relevant text that is quite long and informative."
]
if __name__ == '__main__':
    print(perplexity(texts[0], language_model))