import gradio
from openai import OpenAI
import time
import logging
from typing import List, Dict, Optional, Any
import json

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 创建 OpenAI 客户端，配置 API 密钥和自定义 base_url
client = OpenAI(
    # api_key="sk-8709f7ed33dc402a8a9885a1a8ee403e",
    # base_url="https://dashscope.aliyuncs.com/compatible-mode/v1"
    api_key="sk-164763b2a67641df9171df14c619286d",
    base_url="https://api.deepseek.com/v1"
)

# 测试用例
TEST_CASES = [
    {
        "name": "基础问答测试",
        "input": "你好，请介绍一下你自己",
        "expected_keywords": ["你好", "我是", "助手"]
    },
    {
        "name": "专业知识测试",
        "input": "请解释一下Python中的装饰器是什么？",
        "expected_keywords": ["装饰器", "函数", "Python"]
    },
    {
        "name": "多轮对话测试",
        "input": "我们来玩个游戏，我说一个词，你接下一个词",
        "expected_keywords": ["游戏", "接词"]
    },
    {
        "name": "边界条件测试",
        "input": "a" * 1000,  # 超长输入
        "expected_keywords": []
    }
]

def run_test_cases() -> Dict[str, Any]:
    """运行测试用例并返回结果"""
    results = {}
    for test in TEST_CASES:
        try:
            start_time = time.time()
            response = chat(test["input"], [])
            end_time = time.time()
            
            # 检查响应时间
            response_time = end_time - start_time
            time_check = response_time < 5.0  # 假设5秒为可接受的最大响应时间
            
            # 检查关键词
            keyword_check = all(keyword in response for keyword in test["expected_keywords"])
            
            results[test["name"]] = {
                "passed": time_check and keyword_check,
                "response_time": response_time,
                "response": response
            }
            
            logger.info(f"测试用例 '{test['name']}' 完成，响应时间: {response_time:.2f}秒")
        except Exception as e:
            logger.error(f"测试用例 '{test['name']}' 失败: {str(e)}")
            results[test["name"]] = {
                "passed": False,
                "error": str(e)
            }
    return results


sys_msg = """
你将扮演AI应用工程师的面试人员，回答面试问题。
在回答问题时，请遵循以下指南：
1. 保持专业、客观和自信的语气。
2. 回答要清晰、准确、有逻辑，展示出AI应用工程师的专业素养和知识水平。
3. 如果问题涉及到技术细节，要详细解释相关概念和原理。
4. 尽量提供实际案例或经验来支持你的回答。
5. 避免使用模糊或不确定的语言。
请在<回答>标签内写下你的回答。
"""

def chat(text: str, history: List[Dict[str, str]], 
         temperature: float = 0.7, 
         max_tokens: Optional[int] = None) -> str:
    """
    聊天函数，接收用户输入和历史消息，返回模型回复
    
    参数:
        text: 用户输入的文本
        history: 历史消息列表
        temperature: 温度参数，控制输出的随机性 (0.0-1.0)
        max_tokens: 最大输出token数
    """
    try:
        if not history:
            msg = [{"role": "system", "content": sys_msg}]
        else:
            msg = history.copy()
        start_time = time.time()
        msg.append({"role": "user", "content": text})
        print("*" * 30)
        print(msg)
        print("*" * 30)
        # 添加性能优化的参数
        response = client.chat.completions.create(
            model="deepseek-reasoner",
            messages=msg,
            # extra_body={
            #     # 开启深度思考的参数，对 QwQ 模型无效
            #     "enable_thinking": True,
            #     "enable_search": True,  # 开启联网搜索的参数
            #     "search_options": {
            #         "forced_search": True,  # 强制联网搜索的参数
            #         "search_strategy": "pro",  # 模型将搜索10条互联网信息
            #     }
            # },
            stream=True
        )
        
        end_time = time.time()
        logger.info(f"请求处理时间: {end_time - start_time:.2f}秒")
        
        # 处理流式响应
        content = ""
        for chunk in response:
            if hasattr(chunk, "choices") and chunk.choices and hasattr(chunk.choices[0], "delta"):
                delta = chunk.choices[0].delta
                if hasattr(delta, "content") and delta.content:
                    content += delta.content

        return content
    except Exception as e:
        logger.error(f"聊天处理出错: {str(e)}")
        return f"抱歉，处理您的请求时出现错误: {str(e)}"

def save_test_results(results: Dict[str, bool], filename: str = "test_results.json"):
    """保存测试结果到文件"""
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)

# 创建 Gradio 聊天界面并启动
iframe = gradio.ChatInterface(
    fn=chat,
    type="messages",
    title="Chat bot",
    description="聊天机器人界面",
    examples=[
        ["你好，请介绍一下你自己"],
        ["请解释一下Python中的装饰器是什么？"],
        ["我们来玩个游戏，我说一个词，你接下一个词"]
    ]
)

# 运行测试用例
if __name__ == "__main__":
    iframe.launch()
