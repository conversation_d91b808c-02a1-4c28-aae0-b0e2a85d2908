# 游戏设计文档

## 游戏名称
贪吃蛇

## 游戏规则
1. 玩家通过键盘方向键控制蛇的移动方向。
2. 蛇需要吃掉屏幕上的食物，每吃一个食物，蛇的长度增加一格，得分增加一分。
3. 随着得分增加，蛇的移动速度会逐渐加快。
4. 游戏中有障碍物，蛇碰到障碍物或撞到墙壁会导致游戏结束。
5. 当玩家得分达到10分时，进入下一关卡，障碍物数量增加。
6. 游戏共有10个关卡，玩家通过所有关卡即为胜利。

## 界面布局
1. 游戏界面分为游戏区域和显示栏。
2. 显示栏位于屏幕顶部，显示当前得分和关卡信息。
3. 游戏区域占据显示栏以下的部分，包含蛇、食物和障碍物。

## 主要功能点
1. **蛇的移动**：通过键盘方向键控制蛇的移动方向，蛇每次移动一个单位。
2. **食物生成**：随机生成食物位置，食物被吃掉后重新生成。
3. **障碍物生成**：根据关卡生成不同数量的障碍物，障碍物位置随机。
4. **碰撞检测**：检测蛇是否碰到墙壁、障碍物或自身，碰撞则游戏结束。
5. **关卡管理**：根据得分进入下一关卡，增加障碍物数量，重置蛇的位置和速度。
6. **界面绘制**：绘制蛇、食物、障碍物和显示栏信息。
7. **游戏结束**：显示游戏结束信息，玩家可以选择重新开始或退出游戏。

## 关键步骤需求
1. 用python写一个贪吃蛇小程序
2. 增加规则：蛇每吃一次，移动速度略微增加
```
if x1 == foodx and y1 == foody:
    foodx = round(random.randrange(0, width - snake_block) / 10.0) * 10.0
    foody = round(random.randrange(display_bar_height, height - snake_block) / 10.0) * 10.0
    Length_of_snake += 1
    snake_speed += 1  # 略微增加速度
```
3. 左上角加个计分牌，每吃一个加1分
```
def Your_score(score):
    value = score_font.render("Your Score: " + str(score), True, black)
    dis.blit(value, [0, 0])  # 将分数移动到左上角
    
Your_score(Length_of_snake - 1)  # 显示分数
```
4. 设计10个关卡，从第2关开始每个关卡增加部分障碍物，蛇撞到障碍物则游戏失败。每个关卡取得10分就通关
5. 在标题下增加一个显示栏，显示兰中左边是计分牌，中间显示当前是第几关卡。显示栏下面才是游戏地图
6. 每关开始时蛇恢复到初始长度并停止在初始位置
7. 第1到9关通关后提供按钮让用户选择是否进入下一关
```
if Length_of_snake - 1 >= 10:  # 检查是否完成关卡
                level += 1
                if level > levels:
                    message("You Win! Press Q-Quit or C-Play Again", green)
                    pygame.display.update()
                    time.sleep(2)
                    game_close = True
                else:
                    next_level_prompt()
                    waiting_for_next_level = True
                    while waiting_for_next_level:
                        for event in pygame.event.get():
                            if event.type == pygame.KEYDOWN:
                                if event.key == pygame.K_n:
                                    waiting_for_next_level = False
                                if event.key == pygame.K_q:
                                    game_over = True
                                    game_close = False
                                    waiting_for_next_level = False
                            if event.type == pygame.QUIT:
                                game_over = True
                                game_close = False
                                waiting_for_next_level = False
                    if not game_over:
                        generate_obstacles(level)
                        reset_snake()  # 重置蛇的位置和长度
                        snake_speed = initial_snake_speed  # 将速度重置为初始速度
```

## 错误解决prompt
1. 代码报错SyntaxError: no binding for nonlocal 'snake_List' found
2. 错误：蛇没有碰到下边界游戏就失败了。请检查代码并修正错误