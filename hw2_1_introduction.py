import hw2_0

llm = hw2_0.YYLLM()
'''
应用场景: 智能写作助手
任务描述: 根据用户描述制作出[新闻报道,产品说明,故事创作]文体的高质量的文章片段或全文
'''
# 产品介绍

promt1_1 = (
    '''
    请根据给定的产品生成一篇400字以内的产品介绍
    产品:{text}
    '''
)
promt1_2 = (
    '''
    背景: 你是一名资深产品经理,非常了解{text}这款产品,正在产品发布会上向大家介绍这款产品
    任务: 向用户介绍{text}这款产品,字数严格限制在600字以内.
    原则:
        1. 介绍范围包括但不限于产品外观,各项参数,独特功能
        2. 结构合理,内容连贯,层次清晰
    '''
)
promt1_3 = (
    '''
    背景: 你是一名资深产品经理,非常了解{text}这款产品,正在为产品发布会准备演讲稿
    任务: 向用户介绍{text}这款产品,字数严格限制在600字以内.
    要求:
        1. 介绍范围包括但不限于产品外观,重点指标,独特功能.
        2. 对于重点指标,通过比喻等修辞手法让客户更理解其意义.
        3. 通过与其他品牌产品对比的方式突出产品优势,需指明对比产品名称及其指标值.
        4. 使用可靠的数据来源,不要自己编造内容.
        5. 结构合理,内容连贯,层次清晰.
    '''
)

def gen_text(promt_dic: dict):
    for k, v in promt_dic.items():
        print({k}, ':', '*' * 92)
        print(v)
        print('answer', '*' * 100)
        res = llm.chat(v)
        print(res)


def gen_news(text):
   gen_text({'prompt1_3':promt1_3.format(text=text)})


if __name__ == '__main__':
    gen_news('华为mate70')

'''
{'prompt1_1'} : ********************************************************************************************

    请根据给定的产品生成一篇400字以内的产品介绍
    产品:华为mate70
    
answer ****************************************************************************************************
华为Mate70，智慧新世界的先锋之作。搭载全新麒麟9010处理器，性能强劲，轻松应对各类高负载场景，带来流畅无比的使用体验。配备超可靠玄武架构，耐摔抗冲击，时刻保护手机安全。其红枫原色影像系统，以150万多光谱通道准确识别色彩信息，还原真实世界，让每一拍都惊艳。

华为Mate70不仅拥有出色的性能，更在续航上大有突破。内置5300mAh大容量电池，支持66W华为超级快充，随时随地，疾速充电，让你从早到晚都保持充沛状态。同时，全新鸿蒙AI系统，以智慧赋能生活，带来便捷高效的全新体验。

在设计上，华为Mate70同样不失格调。利落直屏美学，纯粹新色，满溢盎然生机。2500尼特峰值亮度，强光下显示依然清晰。IP68级4米抗水，无惧水花飞尘，处变不惊。

华为Mate70，以性能、影像、续航三大核心优势，重新定义高端智能手机标准。无论你是商务人士，还是影像爱好者，或是游戏玩家，它都能满足你的需求，为你开启智慧生活新篇章。
assessment ****************************************************************************************************
缺少层次感
{'prompt1_2'} : ********************************************************************************************

    背景: 你是一名资深产品经理,非常了解华为mate70这款产品,正在产品发布会上向大家介绍这款产品
    任务: 向用户介绍华为mate70这款产品,字数严格限制在600字以内.
    原则:
        1. 介绍范围包括但不限于产品外观,各项参数,独特功能
        2. 结构合理,内容连贯,层次清晰
    
answer ****************************************************************************************************
各位来宾、朋友们：

大家好！

今天，我非常荣幸地向大家介绍华为全新旗舰产品——华为Mate70。这款手机融合了华为最新的科技与创新，无论是外观设计还是内在性能，都达到了全新的高度。

一眼望去，华为Mate70的外观设计就让人眼前一亮。它采用了全新的工艺，机身线条流畅，手感极佳。屏幕采用了XX寸高清AMOLED全面屏，不仅显示效果出色，而且功耗更低。机身背部采用了独特的材质，既保证了手感，又增添了产品的质感。

在参数方面，华为Mate70搭载了最新的麒麟处理器，运行速度更快，处理能力更强。配备了超大的存储容量，无论是应用、游戏还是高清视频，都能轻松应对。电池续航也进行了大幅提升，支持快速充电技术，让你不再为电量而焦虑。

当然，华为Mate70的独特功能更是其亮点所在。它配备了先进的AI摄像头系统，无论是拍照还是录像，都能呈现出极致的画质。此外，这款手机还支持5G网络，让你在高速网络下畅享各类应用。最值得一提的是，华为Mate70还引入了全新的智能助手功能，通过学习你的使用习惯，为你提供更加个性化的服务。

总的来说，华为Mate70不仅是一款手机，更是一款融合了最新科技与艺术的智能产品。它不仅能满足你的日常需求，更能为你带来前所未有的使用体验。我们相信，华为Mate70将成为你生活中不可或缺的伙伴。

感谢大家的聆听，期待华为Mate70能为你带来全新的智能生活体验！
assessment ****************************************************************************************************
prompt放松字数给予更多发挥空间,限定了产品介绍范围,增加了角色设定,内容结构化; 输出内容更加拟人化,根据限定范围依次做了介绍
{'prompt1_3'} : ********************************************************************************************

    背景: 你是一名资深产品经理,非常了解华为mate70这款产品,正在为产品发布会准备演讲稿
    任务: 向用户介绍华为mate70这款产品,字数严格限制在600字以内.
    要求:
        1. 介绍范围包括但不限于产品外观,重点指标,独特功能.
        2. 对于重点指标,通过比喻等修辞手法让客户更理解其意义.
        3. 通过与其他品牌产品对比的方式突出产品优势,需指明对比产品名称及其指标值.
        4. 使用可靠的数据来源,不要自己编造内容.
        5. 结构合理,内容连贯,层次清晰.
    
answer ****************************************************************************************************
尊敬的各位来宾、媒体朋友们：

大家好！

今天，我荣幸地向大家介绍华为全新旗舰手机——华为Mate70。它不仅继承了华为Mate系列的卓越品质，更在多项重点指标上实现了突破。

华为Mate70在外观设计上采用了全新的工艺，机身线条流畅，色彩搭配典雅，不仅轻薄，而且手感极佳。其屏幕采用了最先进的OLED技术，色彩还原度极高，为用户带来沉浸式的视觉体验。

在性能指标上，华为Mate70搭载了最新一代的麒麟处理器，其计算能力堪比一台小型超级计算机。就像是一辆高性能的跑车，能够在瞬间加速到极限，轻松应对各种高负荷任务。其电池容量也大幅提升，续航时间就如同长跑冠军的耐力，足以支撑一整天的重度使用。

华为Mate70还配备了全新的AI超感光摄像头，能够在各种光线条件下捕捉细节，让每一张照片都如同专业摄影师的作品。与此同时，其独特的夜景模式更是行业内的佼佼者，让黑夜中的景色也能清晰可见。

相比市面上其他旗舰手机，如苹果的iPhone 14 Pro Max，华为Mate70在多项性能测试中表现更优。根据权威的TechRadar评测数据显示，华为Mate70在多任务处理速度和电池续航方面均领先于iPhone 14 Pro Max。

华为Mate70不仅是一款手机，更是一款融合了最新科技与艺术设计的智能设备。它将成为您日常生活中的得力助手，无论是工作还是娱乐，都能为您提供卓越的使用体验。

感谢大家的关注，华为Mate70，期待与您共同探索未来的无限可能！
assessment ****************************************************************************************************
prompt增加了防止编造的规则,产品对比的规则,指标解释的规则,在输出内容中都有得到体现
'''