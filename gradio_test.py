import gradio as gr

with gr.Blocks() as block:
    with gr.Tab(label="text block"):
        with gr.Row():
            with gr.<PERSON>umn(scale=15):
                text1 = gr.Textbox(lines=2)
                text2 = gr.Textbox(lines=2)
            with gr.<PERSON>umn(scale=1,min_width=1):
                btn1 = gr.<PERSON><PERSON>(value="1")
                btn2 = gr.<PERSON><PERSON>(value="2")
                btn3 = gr.<PERSON><PERSON>(value="3")
                btn4 = gr.<PERSON><PERSON>(value="4")
            with gr.<PERSON>umn(scale=6):
                btn_generate = gr.<PERSON><PERSON>("Generate",variant="primary",scale=4)
                with gr.<PERSON>(min_height=1):
                    drop1 = gr.Dropdown(["1", "2", "3"],label="style1",interactive=True)
                    drop2 = gr.Dropdown(["option1", "option2", "option3"],label="style2",interactive=True)

block.launch()