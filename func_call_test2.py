import json
from functools import lru_cache

from openai import OpenAI

def div_func(a: float, b: float):
    """
    除法函数
    """
    return a / b

def mul_func(a: float, b: float):
    """
    乘法函数
    """
    return a * b

def chat_func(text: str):
    """
    聊天函数
    """
    return text

# 定义一个基础的模型类 BaseLLM
class BaseLLM:
    # 使用 lru_cache 装饰器，设置缓存大小为 1024，缓存函数 chat 的返回值
    @lru_cache(maxsize=1024)
    def chat(self, text):
        # 调用内部函数 _chat 处理传入的文本
        return self._chat(text)

    # 定义一个内部函数 _chat，用于处理文本，需要在子类中实现具体逻辑
    def _chat(self, text):
        # 抛出 NotImplementedError 异常，提示子类需要实现该方法
        raise NotImplementedError
# 定义 QwenLLM 类，继承自 BaseLLM， 其接口与gpt系列基本相同
class QwenLLM(BaseLLM):
    def __init__(self):
        # 初始化 QwenLLM 实例，传入环境变量中的 api_key 和 base_url
        self.client = OpenAI(
            api_key="sk-8709f7ed33dc402a8a9885a1a8ee403e",
            base_url="https://dashscope.aliyuncs.com/compatible-mode/v1"
        )


    def chat(self, text, messages=[]):
        if not messages:
            # 如果没有历史消息，则将用户输入作为第一条消息
            messages = [{"role": "user", "content": text}]

        # 使用固定的 engine 发送对话请求，并获取回复结果
        response = self.client.chat.completions.create(
            model="qwen-turbo",
            messages=messages,
            temperature=0.8,
            top_p=0.8
        )

        # 返回回复内容
        return response.choices[0].message.content

class QwenTool(QwenLLM):
    def __init__(self, tools=[]):
        super().__init__()
        self.tools = tools

    def tool_chat(self, text, messages=[]):
        if not messages:
            messages = [{"role": "user", "content": text}]
        # 为了保证实验和课程的结果一致，这里使用了固定的 engine
        response = self.client.chat.completions.create(
            model="qwen-turbo",
            messages=messages,
            tools=self.tools
        )
        print(f"response: {response}")
        return response.choices[0].message
div_tool = {  # 用 JSON 描述函数。可以定义多个。由大模型决定调用谁。也可能都不调用
    "type": "function",
    "function": {
        "name": "div_func",
        "description": "除法函数",
        "parameters": {
            "type": "object",
            "properties": {
                "a": {
                    "type": "number",
                    "description": "被除数",
                },
                "b": {
                    "type": "number",
                    "description": "除数",
                },
            },
        },
    },
}

mul_tool = {  # 用 JSON 描述函数。可以定义多个。由大模型决定调用谁。也可能都不调用
    "type": "function",
    "function": {
        "name": "mul_func",
        "description": "乘法函数",
        "parameters": {
            "type": "object",
            "properties": {
                "a": {
                    "type": "number",
                    "description": "乘数",
                },
                "b": {
                    "type": "number",
                    "description": "乘数",
                },
            },
        },
    },
}

# 创建一个名为 'tools' 的列表，包含两个工具对象 'div_tool' 和 'mul_tool'。
tools = [div_tool, mul_tool]

# 创建一个 'QwenTool' 类的实例 'qwen_tool'，并将 'tools' 列表作为参数传递给这个实例。
qwen_tool = QwenTool(tools=tools)

# 定义一个名为 'func_call' 的函数，它接受一个 'QwenTool' 类型的模型 'model' 和一个字符串 'text' 作为参数。
def func_call(model: QwenTool, text: str):
    """
    调用工具函数
    """
    # 初始化一个空字符串 'output'，用于存储最终输出。
    output = ""
    # 初始化一个消息列表 'messages'，包含一个字典，表示用户的角色和发送的内容。
    messages = [{"role": "user", "content": text}]
    # 初始化一个循环计数器 'loop_cnt'，用于控制循环的次数。
    loop_cnt = 0
    # 使用一个 while 循环，当 'loop_cnt' 小于 10 时继续执行循环。
    while loop_cnt < 10:
        # 增加循环计数器 'loop_cnt' 的值。
        loop_cnt += 1
        # 调用模型的 'tool_chat' 方法，传入当前文本 'text' 和消息列表 'messages'，获取响应。
        llm_response = model.tool_chat(text, messages)
        # 将模型的响应添加到消息列表 'messages' 中。
        messages.append(llm_response)
        # 检查响应中是否包含工具调用 'tool_calls'。
        if llm_response.tool_calls is not None:
            # 如果有工具调用，遍历每个工具调用。
            for tool_call in llm_response.tool_calls:
                # 将工具调用中的参数从 JSON 字符串解析为 Python 字典。
                args = json.loads(tool_call.function.arguments)
                # 检查工具调用的函数名称是否是 'div_func'。
                if tool_call.function.name == "div_func":
                    # 如果是，调用除法函数 'div_func'，并将参数 'a' 和 'b' 转换为浮点数。
                    func_output = div_func(float(args["a"]), float(args["b"]))
                # 检查工具调用的函数名称是否是 'mul_func'。
                elif tool_call.function.name == "mul_func":
                    # 如果是，调用乘法函数 'mul_func'，并将参数 'a' 和 'b' 转换为浮点数。
                    func_output = mul_func(float(args["a"]), float(args["b"]))
                # 将工具调用的结果添加到消息列表 'messages' 中。
                messages.append({
                    "tool_call_id": tool_call.id,
                    "role": "tool",
                    "name": tool_call.function.name,
                    "content": str(func_output)
                })
                # 打印工具调用的详细信息，包括函数名称、输入参数和输出结果。
                print(f"调用了 {tool_call.function.name} 函数，输入参数为 {args}，输出结果为 {func_output}")
        # 如果响应中没有工具调用，结束循环。
        else:
            break
    # 将消息列表中最后一个消息的内容赋值给 'output' 变量。
    output = messages[-1].content
    # 返回最终的输出结果 'output'。
    return output

if __name__ == '__main__':
    func_call(qwen_tool, "1.5 除以 2.5 再乘以 3.5 是多少？")