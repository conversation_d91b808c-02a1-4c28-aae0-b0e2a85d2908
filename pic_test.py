import os

import dashscope

import base64

from dashscope import Generation, MultiModalConversation


def find_image_with_keywords(directory: str) -> str:
    """
    遍历指定目录中的所有图片，调用 chat_with_bailian_image 检查图片中是否包含指定关键词。

    Args:
        directory (str): 图片所在的目录路径
        keywords (list[str]): 要检查的关键词列表

    Returns:
        str: 包含关键词的图片文件名，如果未找到则返回空字符串
    """
    for filename in os.listdir(directory):
        if filename.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp', '.gif')):
            image_path = os.path.join(directory, filename)
            print(image_path)
            print(analyze_image(image_path))
            print("end")
def analyze_image(image_path):

    # 调用多模态模型
    response = MultiModalConversation.call(
        model="qwen-vl-ocr-latest",  # 通义千问视觉模型
        api_key='sk-594ed6e5623849e68ea059bff1f14f56',
        messages=[
            {
                "role": "user",
                "content": [
                    {
                        "image": f"file://{image_path}",
                    },
                    # qwen-vl-ocr-latest未设置内置任务时，支持在以下text字段中传入Prompt，若未传入则使用默认的Prompt：Please output only the text content from the image without any additional descriptions or formatting.
                    # 如调用qwen-vl-ocr-1028，模型会使用固定Prompt：Read all the text in the image.，不支持用户在text中传入自定义Prompt
                    {
                        "text": "请识别图片中是否有穿搭,穿着搭配或与之相关的词汇"
                    },
                ]
            }
        ]
    )

    print(response["output"]["choices"][0]["message"].content[0]["text"])

# 使用示例
if __name__ == "__main__":
    find_image_with_keywords("F:\日报")